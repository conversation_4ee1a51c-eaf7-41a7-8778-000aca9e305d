 <?php $__env->startSection('content'); ?>
<?php if(session()->has('not_permitted')): ?>
  <div class="alert alert-danger alert-dismissible text-center"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button><?php echo e(session()->get('not_permitted')); ?></div>
<?php endif; ?>
<section class="forms">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <h4><?php echo e(trans('file.Update Customer')); ?></h4>
                    </div>
                    <div class="card-body">
                        <p class="italic"><small><?php echo e(trans('file.The field labels marked with * are required input fields')); ?>.</small></p>
                        <?php echo Form::open(['route' => ['customer.update',$lims_customer_data->id], 'method' => 'put', 'files' => true]); ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <input type="hidden" name="customer_group" value="<?php echo e($lims_customer_data->customer_group_id); ?>">
                                    <label><?php echo e(trans('file.Customer Group')); ?> *</strong> </label>
                                    <select required class="form-control selectpicker" name="customer_group_id">
                                        <?php $__currentLoopData = $lims_customer_group_all; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $customer_group): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($customer_group->id); ?>"><?php echo e($customer_group->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.name')); ?> *</strong> </label>
                                    <input type="text" name="customer_name" value="<?php echo e($lims_customer_data->name); ?>" required class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Company Name')); ?> </label>
                                    <input type="text" name="company_name" value="<?php echo e($lims_customer_data->company_name); ?>" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Email')); ?></label>
                                    <input type="email" name="email" value="<?php echo e($lims_customer_data->email); ?>" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Phone Number')); ?> *</label>
                                    <input type="text" name="phone_number" required value="<?php echo e($lims_customer_data->phone_number); ?>" class="form-control">
                                    <?php if($errors->has('phone_number')): ?>
                                   <span>
                                       <strong><?php echo e($errors->first('phone_number')); ?></strong>
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Tax Number')); ?></label>
                                    <input type="text" name="tax_no" class="form-control" value="<?php echo e($lims_customer_data->tax_no); ?>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Address')); ?></label>
                                    <input type="text" name="address" value="<?php echo e($lims_customer_data->address); ?>" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.City')); ?></label>
                                    <input type="text" name="city" value="<?php echo e($lims_customer_data->city); ?>" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.State')); ?></label>
                                    <input type="text" name="state" value="<?php echo e($lims_customer_data->state); ?>" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Postal Code')); ?></label>
                                    <input type="text" name="postal_code" value="<?php echo e($lims_customer_data->postal_code); ?>" class="form-control">
                                </div>
                            </div>
                            <?php if(!$lims_customer_data->user_id): ?>
                            <div class="col-md-6 mt-3">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Add User')); ?></label>&nbsp;
                                    <input type="checkbox" name="user" value="1" />
                                </div>
                            </div>
                            <?php endif; ?>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Country')); ?></label>
                                    <input type="text" name="country" value="<?php echo e($lims_customer_data->country); ?>" class="form-control">
                                </div>
                            </div>
                            <?php $__currentLoopData = $custom_fields; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $field): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php $field_name = str_replace(' ', '_', strtolower($field->name)); ?>
                                <?php if(!$field->is_admin || \Auth::user()->role_id == 1): ?>
                                    <div class="<?php echo e('col-md-'.$field->grid_value); ?>">
                                        <div class="form-group">
                                            <label><?php echo e($field->name); ?></label>
                                            <?php if($field->type == 'text'): ?>
                                                <input type="text" name="<?php echo e($field_name); ?>" value="<?php echo e($lims_customer_data->$field_name); ?>" class="form-control" <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?>>
                                            <?php elseif($field->type == 'number'): ?>
                                                <input type="number" name="<?php echo e($field_name); ?>" value="<?php echo e($lims_customer_data->$field_name); ?>" class="form-control" <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?>>
                                            <?php elseif($field->type == 'textarea'): ?>
                                                <textarea rows="5" name="<?php echo e($field_name); ?>" value="<?php echo e($lims_customer_data->$field_name); ?>" class="form-control" <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?>></textarea>
                                            <?php elseif($field->type == 'checkbox'): ?>
                                                <br>
                                                <?php
                                                $option_values = explode(",", $field->option_value);
                                                $field_values =  explode(",", $lims_customer_data->$field_name);
                                                ?>
                                                <?php $__currentLoopData = $option_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <label>
                                                        <input type="checkbox" name="<?php echo e($field_name); ?>[]" value="<?php echo e($value); ?>" <?php if(in_array($value, $field_values)): ?> checked <?php endif; ?> <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?>> <?php echo e($value); ?>

                                                    </label>
                                                    &nbsp;
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php elseif($field->type == 'radio_button'): ?>
                                                <br>
                                                <?php 
                                                $option_values = explode(",", $field->option_value);
                                                ?>
                                                <?php $__currentLoopData = $option_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <label class="radio-inline">
                                                        <input type="radio" name="<?php echo e($field_name); ?>" value="<?php echo e($value); ?>" <?php if($value == $lims_customer_data->$field_name): ?><?php echo e('checked'); ?><?php endif; ?> <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?>> <?php echo e($value); ?>

                                                    </label>
                                                    &nbsp;
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php elseif($field->type == 'select'): ?>
                                                <?php $option_values = explode(",", $field->option_value); ?>
                                                <select class="form-control" name="<?php echo e($field_name); ?>" <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?>>
                                                    <?php $__currentLoopData = $option_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($value); ?>" <?php if($value == $lims_customer_data->$field_name): ?><?php echo e('selected'); ?><?php endif; ?>><?php echo e($value); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            <?php elseif($field->type == 'multi_select'): ?>
                                                <?php
                                                $option_values = explode(",", $field->option_value);
                                                $field_values =  explode(",", $lims_customer_data->$field_name);
                                                ?>
                                                <select class="form-control" name="<?php echo e($field_name); ?>[]" <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?> multiple>
                                                    <?php $__currentLoopData = $option_values; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($value); ?>" <?php if(in_array($value, $field_values)): ?> selected <?php endif; ?>><?php echo e($value); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            <?php elseif($field->type == 'date_picker'): ?>
                                                <input type="text" name="<?php echo e($field_name); ?>" value="<?php echo e($lims_customer_data->$field_name); ?>" class="form-control date" <?php if($field->is_required): ?><?php echo e('required'); ?><?php endif; ?>>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 user-input">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.UserName')); ?> *</label>
                                    <input type="text" name="name" class="form-control">
                                    <?php if($errors->has('name')): ?>
                                   <span>
                                       <strong><?php echo e($errors->first('name')); ?></strong>
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-6 user-input">
                                <div class="form-group">
                                    <label><?php echo e(trans('file.Password')); ?> *</label>
                                    <input type="password" name="password" class="form-control">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group mt-3">
                                    <input type="submit" value="<?php echo e(trans('file.submit')); ?>" class="btn btn-primary">
                                </div>
                            </div>
                        </div>
                        <?php echo Form::close(); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script type="text/javascript">

    $("ul#people").siblings('a').attr('aria-expanded','true');
    $("ul#people").addClass("show");

    $(".user-input").hide();

    $('input[name="user"]').on('change', function() {
        if ($(this).is(':checked')) {
            $('.user-input').show(300);
            $('input[name="name"]').prop('required',true);
            $('input[name="password"]').prop('required',true);
        }
        else{
            $('.user-input').hide(300);
            $('input[name="name"]').prop('required',false);
            $('input[name="password"]').prop('required',false);
        }
    });

    var customer_group = $("input[name='customer_group']").val();
    $('select[name=customer_group_id]').val(customer_group);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layout.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\sale\resources\views/backend/customer/edit.blade.php ENDPATH**/ ?>