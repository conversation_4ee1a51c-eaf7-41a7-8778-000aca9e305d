

CREATE TABLE `accounts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `account_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `initial_balance` double DEFAULT NULL,
  `total_balance` double NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `is_default` tinyint(1) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Bank Account',
  `parent_account_id` int DEFAULT NULL,
  `is_payment` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO accounts VALUES("1","10010","Sales Account","0","0","","1","1","2023-09-17 02:54:16","2023-09-17 02:54:16","","Bank Account","","1");
INSERT INTO accounts VALUES("2","20010","Expense Account","0","0","","","1","2023-09-17 02:54:34","2023-09-17 02:54:34","","Bank Account","","1");



CREATE TABLE `adjustments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `warehouse_id` int NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `total_qty` double NOT NULL,
  `item` int NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `attendances` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `employee_id` int NOT NULL,
  `user_id` int NOT NULL,
  `checkin` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checkout` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` int NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `barcodes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `width` double(22,4) DEFAULT NULL,
  `height` double(22,4) DEFAULT NULL,
  `paper_width` double(22,4) DEFAULT NULL,
  `paper_height` double(22,4) DEFAULT NULL,
  `top_margin` double(22,4) DEFAULT NULL,
  `left_margin` double(22,4) DEFAULT NULL,
  `row_distance` double(22,4) DEFAULT NULL,
  `col_distance` double(22,4) DEFAULT NULL,
  `stickers_in_one_row` int DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_continuous` tinyint(1) NOT NULL DEFAULT '0',
  `stickers_in_one_sheet` int DEFAULT NULL,
  `is_custom` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `billers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `vat_number` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO billers VALUES("1","Default Biller","","My Company","","<EMAIL>","###","Somewhere","Geo","","","","1","2023-09-17 02:51:11","2023-09-17 02:51:11");



CREATE TABLE `brands` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `cash_registers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `cash_in_hand` double NOT NULL,
  `user_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `status` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO cash_registers VALUES("1","0","1","1","1","2024-10-12 16:25:52","2024-10-12 16:25:52");
INSERT INTO cash_registers VALUES("2","0","1","1","1","2024-10-12 16:25:52","2024-10-12 16:25:52");



CREATE TABLE `categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_id` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `challans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `courier_id` int NOT NULL,
  `packing_slip_list` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount_list` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `cash_list` longtext COLLATE utf8mb4_unicode_ci,
  `online_payment_list` longtext COLLATE utf8mb4_unicode_ci,
  `cheque_list` longtext COLLATE utf8mb4_unicode_ci,
  `delivery_charge_list` longtext COLLATE utf8mb4_unicode_ci,
  `status_list` longtext COLLATE utf8mb4_unicode_ci,
  `closing_date` date DEFAULT NULL,
  `created_by_id` int NOT NULL,
  `closed_by_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `coupons` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` double NOT NULL,
  `minimum_amount` double DEFAULT NULL,
  `quantity` int NOT NULL,
  `used` int NOT NULL,
  `expired_date` date NOT NULL,
  `user_id` int NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `couriers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO couriers VALUES("1","Default Courier","000","Local","1","2023-09-17 02:57:42","2023-09-17 02:57:42");



CREATE TABLE `currencies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `exchange_rate` double NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO currencies VALUES("1","USD","$","1","1","2023-06-21 16:19:32","2023-08-08 00:37:37");
INSERT INTO currencies VALUES("2","Pakistani Rupee","PKR","2","1","2024-10-12 15:29:08","2024-10-12 15:29:08");



CREATE TABLE `custom_fields` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `belongs_to` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `default_value` text COLLATE utf8mb4_unicode_ci,
  `option_value` text COLLATE utf8mb4_unicode_ci,
  `grid_value` int NOT NULL,
  `is_table` tinyint(1) NOT NULL,
  `is_invoice` tinyint(1) NOT NULL,
  `is_required` tinyint(1) NOT NULL,
  `is_admin` tinyint(1) NOT NULL,
  `is_disable` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `customer_groups` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `percentage` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO customer_groups VALUES("1","General Customers","0","1","2023-09-17 02:51:59","2023-09-17 02:51:59");
INSERT INTO customer_groups VALUES("2","Wholesale Customers","5","1","2023-09-17 02:52:22","2023-09-17 02:52:22");



CREATE TABLE `customers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `customer_group_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tax_no` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `points` double DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deposit` double DEFAULT NULL,
  `expense` double DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO customers VALUES("1","1","","Walk-in Customer","","","###","","@@@","Local","","","","","1","2023-09-17 02:53:07","2023-09-17 02:53:07","","");



CREATE TABLE `deliveries` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sale_id` int NOT NULL,
  `packing_slip_ids` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `courier_id` int DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `delivered_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `recieved_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `departments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO departments VALUES("1","Administration","1","2023-09-17 02:32:21","2023-09-17 02:32:21");
INSERT INTO departments VALUES("2","Sales and Marketing","1","2023-09-17 02:32:33","2023-09-17 02:32:33");
INSERT INTO departments VALUES("3","Huma Resource","1","2023-09-17 02:32:44","2023-09-17 02:32:44");
INSERT INTO departments VALUES("4","Finance","1","2023-09-17 02:32:51","2023-09-17 02:32:51");



CREATE TABLE `deposits` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `amount` double NOT NULL,
  `customer_id` int NOT NULL,
  `user_id` int NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `discount_plan_customers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `discount_plan_id` int NOT NULL,
  `customer_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `discount_plan_discounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `discount_id` int NOT NULL,
  `discount_plan_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `discount_plans` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `discounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `applicable_for` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_list` longtext COLLATE utf8mb4_unicode_ci,
  `valid_from` date NOT NULL,
  `valid_till` date NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` double NOT NULL,
  `minimum_qty` double NOT NULL,
  `maximum_qty` double NOT NULL,
  `days` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `dso_alerts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_info` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `number_of_products` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `employees` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `department_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `staff_id` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `expense_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO expense_categories VALUES("1","20010","General Expense","1","2023-09-17 02:55:59","2023-09-17 02:55:59");
INSERT INTO expense_categories VALUES("2","20020","Utility Bills","1","2023-09-17 02:56:21","2023-09-17 02:56:21");
INSERT INTO expense_categories VALUES("3","20030","Payroll Expense","1","2023-09-17 02:56:37","2023-09-17 02:56:37");



CREATE TABLE `expenses` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expense_category_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `account_id` int NOT NULL,
  `user_id` int NOT NULL,
  `cash_register_id` int DEFAULT NULL,
  `amount` double NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `external_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `details` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `module_status` json DEFAULT NULL,
  `active` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `connection` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `general_settings` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `site_title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `site_logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_rtl` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `currency` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `package_id` int DEFAULT NULL,
  `subscription_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `staff_access` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `without_stock` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'no',
  `date_format` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `developed_by` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `invoice_format` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `decimal` int DEFAULT '2',
  `state` int DEFAULT NULL,
  `theme` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `modules` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `currency_position` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiry_date` date DEFAULT NULL,
  `expiry_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'days',
  `expiry_value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `is_zatca` tinyint(1) DEFAULT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `vat_registration_number` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_packing_slip` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  CONSTRAINT `general_settings_chk_1` CHECK (json_valid(`modules`))
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO general_settings VALUES("1","myCafee","20250711060809.png","0","2023-06-21 02:15:00","2025-07-11 06:09:15","1","","","own","no","Y-m-d","ReberKurdi","standard","2","1","default.css","","prefix","","days","0","0","www.appkurdi.com","","0");



CREATE TABLE `gift_card_recharges` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `gift_card_id` int NOT NULL,
  `amount` double NOT NULL,
  `user_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `gift_cards` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `card_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `amount` double NOT NULL,
  `expense` double NOT NULL,
  `customer_id` int DEFAULT NULL,
  `user_id` int DEFAULT NULL,
  `expired_date` date DEFAULT NULL,
  `created_by` int NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `holidays` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `from_date` date NOT NULL,
  `to_date` date NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `is_approved` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `hrm_settings` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `checkin` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `checkout` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `income_categories` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `incomes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `income_category_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `account_id` int NOT NULL,
  `user_id` int NOT NULL,
  `cash_register_id` int DEFAULT NULL,
  `amount` double NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `languages` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO languages VALUES("1","en","2018-07-08 01:14:17","2019-12-24 19:49:20");



CREATE TABLE `mail_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `driver` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `host` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `encryption` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=199 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO migrations VALUES("1","2014_10_12_000000_create_users_table","1");
INSERT INTO migrations VALUES("2","2014_10_12_100000_create_password_resets_table","1");
INSERT INTO migrations VALUES("3","2018_02_17_060412_create_categories_table","1");
INSERT INTO migrations VALUES("4","2018_02_20_035727_create_brands_table","1");
INSERT INTO migrations VALUES("5","2018_02_25_100635_create_suppliers_table","1");
INSERT INTO migrations VALUES("6","2018_02_27_101619_create_warehouse_table","1");
INSERT INTO migrations VALUES("7","2018_03_03_040448_create_units_table","1");
INSERT INTO migrations VALUES("8","2018_03_04_041317_create_taxes_table","1");
INSERT INTO migrations VALUES("9","2018_03_10_061915_create_customer_groups_table","1");
INSERT INTO migrations VALUES("10","2018_03_10_090534_create_customers_table","1");
INSERT INTO migrations VALUES("11","2018_03_11_095547_create_billers_table","1");
INSERT INTO migrations VALUES("12","2018_04_05_054401_create_products_table","1");
INSERT INTO migrations VALUES("13","2018_04_06_133606_create_purchases_table","1");
INSERT INTO migrations VALUES("14","2018_04_06_154600_create_product_purchases_table","1");
INSERT INTO migrations VALUES("15","2018_04_06_154915_create_product_warhouse_table","1");
INSERT INTO migrations VALUES("16","2018_04_10_085927_create_sales_table","1");
INSERT INTO migrations VALUES("17","2018_04_10_090133_create_product_sales_table","1");
INSERT INTO migrations VALUES("18","2018_04_10_090254_create_payments_table","1");
INSERT INTO migrations VALUES("19","2018_04_10_090341_create_payment_with_cheque_table","1");
INSERT INTO migrations VALUES("20","2018_04_10_090509_create_payment_with_credit_card_table","1");
INSERT INTO migrations VALUES("21","2018_04_13_121436_create_quotation_table","1");
INSERT INTO migrations VALUES("22","2018_04_13_122324_create_product_quotation_table","1");
INSERT INTO migrations VALUES("23","2018_04_14_121802_create_transfers_table","1");
INSERT INTO migrations VALUES("24","2018_04_14_121913_create_product_transfer_table","1");
INSERT INTO migrations VALUES("25","2018_05_13_082847_add_payment_id_and_change_sale_id_to_payments_table","1");
INSERT INTO migrations VALUES("26","2018_05_13_090906_change_customer_id_to_payment_with_credit_card_table","1");
INSERT INTO migrations VALUES("27","2018_05_20_054532_create_adjustments_table","1");
INSERT INTO migrations VALUES("28","2018_05_20_054859_create_product_adjustments_table","1");
INSERT INTO migrations VALUES("29","2018_05_21_163419_create_returns_table","1");
INSERT INTO migrations VALUES("30","2018_05_21_163443_create_product_returns_table","1");
INSERT INTO migrations VALUES("31","2018_06_02_050905_create_roles_table","1");
INSERT INTO migrations VALUES("32","2018_06_02_073430_add_columns_to_users_table","1");
INSERT INTO migrations VALUES("33","2018_06_03_053738_create_permission_tables","1");
INSERT INTO migrations VALUES("34","2018_06_21_063736_create_pos_setting_table","1");
INSERT INTO migrations VALUES("35","2018_06_21_094155_add_user_id_to_sales_table","1");
INSERT INTO migrations VALUES("36","2018_06_21_101529_add_user_id_to_purchases_table","1");
INSERT INTO migrations VALUES("37","2018_06_21_103512_add_user_id_to_transfers_table","1");
INSERT INTO migrations VALUES("38","2018_06_23_061058_add_user_id_to_quotations_table","1");
INSERT INTO migrations VALUES("39","2018_06_23_082427_add_is_deleted_to_users_table","1");
INSERT INTO migrations VALUES("40","2018_06_25_043308_change_email_to_users_table","1");
INSERT INTO migrations VALUES("41","2018_07_06_115449_create_general_settings_table","1");
INSERT INTO migrations VALUES("42","2018_07_08_043944_create_languages_table","1");
INSERT INTO migrations VALUES("43","2018_07_11_102144_add_user_id_to_returns_table","1");
INSERT INTO migrations VALUES("44","2018_07_11_102334_add_user_id_to_payments_table","1");
INSERT INTO migrations VALUES("45","2018_07_22_130541_add_digital_to_products_table","1");
INSERT INTO migrations VALUES("46","2018_07_24_154250_create_deliveries_table","1");
INSERT INTO migrations VALUES("47","2018_08_16_053336_create_expense_categories_table","1");
INSERT INTO migrations VALUES("48","2018_08_17_115415_create_expenses_table","1");
INSERT INTO migrations VALUES("49","2018_08_18_050418_create_gift_cards_table","1");
INSERT INTO migrations VALUES("50","2018_08_19_063119_create_payment_with_gift_card_table","1");
INSERT INTO migrations VALUES("51","2018_08_25_042333_create_gift_card_recharges_table","1");
INSERT INTO migrations VALUES("52","2018_08_25_101354_add_deposit_expense_to_customers_table","1");
INSERT INTO migrations VALUES("53","2018_08_26_043801_create_deposits_table","1");
INSERT INTO migrations VALUES("54","2018_09_02_044042_add_keybord_active_to_pos_setting_table","1");
INSERT INTO migrations VALUES("55","2018_09_09_092713_create_payment_with_paypal_table","1");
INSERT INTO migrations VALUES("56","2018_09_10_051254_add_currency_to_general_settings_table","1");
INSERT INTO migrations VALUES("57","2018_10_22_084118_add_biller_and_store_id_to_users_table","1");
INSERT INTO migrations VALUES("58","2018_10_26_034927_create_coupons_table","1");
INSERT INTO migrations VALUES("59","2018_10_27_090857_add_coupon_to_sales_table","1");
INSERT INTO migrations VALUES("60","2018_11_07_070155_add_currency_position_to_general_settings_table","1");
INSERT INTO migrations VALUES("61","2018_11_19_094650_add_combo_to_products_table","1");
INSERT INTO migrations VALUES("62","2018_12_09_043712_create_accounts_table","1");
INSERT INTO migrations VALUES("63","2018_12_17_112253_add_is_default_to_accounts_table","1");
INSERT INTO migrations VALUES("64","2018_12_19_103941_add_account_id_to_payments_table","1");
INSERT INTO migrations VALUES("65","2018_12_20_065900_add_account_id_to_expenses_table","1");
INSERT INTO migrations VALUES("66","2018_12_20_082753_add_account_id_to_returns_table","1");
INSERT INTO migrations VALUES("67","2018_12_26_064330_create_return_purchases_table","1");
INSERT INTO migrations VALUES("68","2018_12_26_144708_create_purchase_product_return_table","1");
INSERT INTO migrations VALUES("69","2018_12_27_110018_create_departments_table","1");
INSERT INTO migrations VALUES("70","2018_12_30_054844_create_employees_table","1");
INSERT INTO migrations VALUES("71","2018_12_31_125210_create_payrolls_table","1");
INSERT INTO migrations VALUES("72","2018_12_31_150446_add_department_id_to_employees_table","1");
INSERT INTO migrations VALUES("73","2019_01_01_062708_add_user_id_to_expenses_table","1");
INSERT INTO migrations VALUES("74","2019_01_02_075644_create_hrm_settings_table","1");
INSERT INTO migrations VALUES("75","2019_01_02_090334_create_attendances_table","1");
INSERT INTO migrations VALUES("76","2019_01_27_160956_add_three_columns_to_general_settings_table","1");
INSERT INTO migrations VALUES("77","2019_02_15_183303_create_stock_counts_table","1");
INSERT INTO migrations VALUES("78","2019_02_17_101604_add_is_adjusted_to_stock_counts_table","1");
INSERT INTO migrations VALUES("79","2019_04_13_101707_add_tax_no_to_customers_table","1");
INSERT INTO migrations VALUES("80","2019_08_19_000000_create_failed_jobs_table","1");
INSERT INTO migrations VALUES("81","2019_10_14_111455_create_holidays_table","1");
INSERT INTO migrations VALUES("82","2019_11_13_145619_add_is_variant_to_products_table","1");
INSERT INTO migrations VALUES("83","2019_11_13_150206_create_product_variants_table","1");
INSERT INTO migrations VALUES("84","2019_11_13_153828_create_variants_table","1");
INSERT INTO migrations VALUES("85","2019_11_25_134041_add_qty_to_product_variants_table","1");
INSERT INTO migrations VALUES("86","2019_11_25_134922_add_variant_id_to_product_purchases_table","1");
INSERT INTO migrations VALUES("87","2019_11_25_145341_add_variant_id_to_product_warehouse_table","1");
INSERT INTO migrations VALUES("88","2019_11_29_182201_add_variant_id_to_product_sales_table","1");
INSERT INTO migrations VALUES("89","2019_12_04_121311_add_variant_id_to_product_quotation_table","1");
INSERT INTO migrations VALUES("90","2019_12_05_123802_add_variant_id_to_product_transfer_table","1");
INSERT INTO migrations VALUES("91","2019_12_08_114954_add_variant_id_to_product_returns_table","1");
INSERT INTO migrations VALUES("92","2019_12_08_203146_add_variant_id_to_purchase_product_return_table","1");
INSERT INTO migrations VALUES("93","2020_02_28_103340_create_money_transfers_table","1");
INSERT INTO migrations VALUES("94","2020_07_01_193151_add_image_to_categories_table","1");
INSERT INTO migrations VALUES("95","2020_09_26_130426_add_user_id_to_deliveries_table","1");
INSERT INTO migrations VALUES("96","2020_10_11_125457_create_cash_registers_table","1");
INSERT INTO migrations VALUES("97","2020_10_13_155019_add_cash_register_id_to_sales_table","1");
INSERT INTO migrations VALUES("98","2020_10_13_172624_add_cash_register_id_to_returns_table","1");
INSERT INTO migrations VALUES("99","2020_10_17_212338_add_cash_register_id_to_payments_table","1");
INSERT INTO migrations VALUES("100","2020_10_18_124200_add_cash_register_id_to_expenses_table","1");
INSERT INTO migrations VALUES("101","2020_10_21_121632_add_developed_by_to_general_settings_table","1");
INSERT INTO migrations VALUES("102","2020_10_30_135557_create_notifications_table","1");
INSERT INTO migrations VALUES("103","2020_11_01_044954_create_currencies_table","1");
INSERT INTO migrations VALUES("104","2020_11_01_140736_add_price_to_product_warehouse_table","1");
INSERT INTO migrations VALUES("105","2020_11_02_050633_add_is_diff_price_to_products_table","1");
INSERT INTO migrations VALUES("106","2020_11_09_055222_add_user_id_to_customers_table","1");
INSERT INTO migrations VALUES("107","2020_11_17_054806_add_invoice_format_to_general_settings_table","1");
INSERT INTO migrations VALUES("108","2021_02_10_074859_add_variant_id_to_product_adjustments_table","1");
INSERT INTO migrations VALUES("109","2021_03_07_093606_create_product_batches_table","1");
INSERT INTO migrations VALUES("110","2021_03_07_093759_add_product_batch_id_to_product_warehouse_table","1");
INSERT INTO migrations VALUES("111","2021_03_07_093900_add_product_batch_id_to_product_purchases_table","1");
INSERT INTO migrations VALUES("112","2021_03_11_132603_add_product_batch_id_to_product_sales_table","1");
INSERT INTO migrations VALUES("113","2021_03_25_125421_add_is_batch_to_products_table","1");
INSERT INTO migrations VALUES("114","2021_05_19_120127_add_product_batch_id_to_product_returns_table","1");
INSERT INTO migrations VALUES("115","2021_05_22_105611_add_product_batch_id_to_purchase_product_return_table","1");
INSERT INTO migrations VALUES("116","2021_05_23_124848_add_product_batch_id_to_product_transfer_table","1");
INSERT INTO migrations VALUES("117","2021_05_26_153106_add_product_batch_id_to_product_quotation_table","1");
INSERT INTO migrations VALUES("118","2021_06_08_213007_create_reward_point_settings_table","1");
INSERT INTO migrations VALUES("119","2021_06_16_104155_add_points_to_customers_table","1");
INSERT INTO migrations VALUES("120","2021_06_17_101057_add_used_points_to_payments_table","1");
INSERT INTO migrations VALUES("121","2021_07_06_132716_add_variant_list_to_products_table","1");
INSERT INTO migrations VALUES("122","2021_09_27_161141_add_is_imei_to_products_table","1");
INSERT INTO migrations VALUES("123","2021_09_28_170052_add_imei_number_to_product_warehouse_table","1");
INSERT INTO migrations VALUES("124","2021_09_28_170126_add_imei_number_to_product_purchases_table","1");
INSERT INTO migrations VALUES("125","2021_10_03_170652_add_imei_number_to_product_sales_table","1");
INSERT INTO migrations VALUES("126","2021_10_10_145214_add_imei_number_to_product_returns_table","1");
INSERT INTO migrations VALUES("127","2021_10_11_104504_add_imei_number_to_product_transfer_table","1");
INSERT INTO migrations VALUES("128","2021_10_12_160107_add_imei_number_to_purchase_product_return_table","1");
INSERT INTO migrations VALUES("129","2021_10_12_205146_add_is_rtl_to_general_settings_table","1");
INSERT INTO migrations VALUES("130","2022_01_13_191242_create_discount_plans_table","1");
INSERT INTO migrations VALUES("131","2022_01_14_174318_create_discount_plan_customers_table","1");
INSERT INTO migrations VALUES("132","2022_01_14_202439_create_discounts_table","1");
INSERT INTO migrations VALUES("133","2022_01_16_153506_create_discount_plan_discounts_table","1");
INSERT INTO migrations VALUES("134","2022_02_05_174210_add_order_discount_type_and_value_to_sales_table","1");
INSERT INTO migrations VALUES("135","2022_05_26_195506_add_daily_sale_objective_to_products_table","1");
INSERT INTO migrations VALUES("136","2022_05_28_104209_create_dso_alerts_table","1");
INSERT INTO migrations VALUES("137","2022_06_01_112100_add_is_embeded_to_products_table","1");
INSERT INTO migrations VALUES("138","2022_06_14_130505_add_sale_id_to_returns_table","1");
INSERT INTO migrations VALUES("139","2022_07_19_115504_add_variant_data_to_products_table","1");
INSERT INTO migrations VALUES("140","2022_07_25_194300_add_additional_cost_to_product_variants_table","1");
INSERT INTO migrations VALUES("141","2022_09_04_195610_add_purchase_id_to_return_purchases_table","1");
INSERT INTO migrations VALUES("142","2023_01_18_125040_alter_table_general_settings","1");
INSERT INTO migrations VALUES("143","2023_01_18_133701_alter_table_pos_setting","1");
INSERT INTO migrations VALUES("144","2023_01_25_145309_add_expiry_date_to_general_settings_table","1");
INSERT INTO migrations VALUES("145","2023_02_23_125656_alter_table_sales","1");
INSERT INTO migrations VALUES("146","2023_02_26_124100_add_package_id_to_general_settings_table","1");
INSERT INTO migrations VALUES("147","2023_03_04_120325_create_custom_fields_table","1");
INSERT INTO migrations VALUES("148","2023_03_22_174352_add_currency_id_and_exchange_rate_to_returns_table","1");
INSERT INTO migrations VALUES("149","2023_03_27_114320_add_currency_id_and_exchange_rate_to_purchases_table","1");
INSERT INTO migrations VALUES("150","2023_03_27_132747_add_currency_id_and_exchange_rate_to_return_purchases_table","1");
INSERT INTO migrations VALUES("151","2023_04_25_150236_create_mail_settings_table","1");
INSERT INTO migrations VALUES("152","2023_05_13_125424_add_zatca_to_general_settings_table","1");
INSERT INTO migrations VALUES("153","2023_05_28_155540_create_tables_table","1");
INSERT INTO migrations VALUES("154","2023_05_29_115039_add_is_table_to_pos_setting_table","1");
INSERT INTO migrations VALUES("155","2023_05_29_115301_add_table_id_to_sales_table","1");
INSERT INTO migrations VALUES("156","2023_05_31_165049_add_queue_no_to_sales_table","1");
INSERT INTO migrations VALUES("157","2023_07_23_160254_create_couriers_table","1");
INSERT INTO migrations VALUES("158","2023_07_23_174343_add_courier_id_to_deliveries_table","1");
INSERT INTO migrations VALUES("159","2023_08_12_124016_add_staff_id_to_employees_table","1");
INSERT INTO migrations VALUES("160","2023_08_14_142608_add_is_active_to_currencies_table","1");
INSERT INTO migrations VALUES("161","2023_08_24_130203_change_columns_to_attendances_table","1");
INSERT INTO migrations VALUES("162","2023_09_10_134503_add_without_stock_to_general_settings_table","1");
INSERT INTO migrations VALUES("163","2023_09_26_211542_add_modules_to_general_settings_table","2");
INSERT INTO migrations VALUES("164","2023_10_15_124306_add_return_qty_to_product_sales_table","2");
INSERT INTO migrations VALUES("165","2023_03_14_174658_add_subscription_type_to_general_setting_table","3");
INSERT INTO migrations VALUES("166","2023_12_03_235606_crete_external_services_table","3");
INSERT INTO migrations VALUES("167","2024_02_04_131826_add_unit_cost_to_product_adjustments_table","3");
INSERT INTO migrations VALUES("168","2024_02_13_173126_change_modules_to_general_settings_table","3");
INSERT INTO migrations VALUES("169","2024_05_02_114215_add_payment_receiver_to_payments","3");
INSERT INTO migrations VALUES("170","2024_05_06_132553_create_sms_templates_table","3");
INSERT INTO migrations VALUES("171","2024_05_07_102225_add_send_sms_to_pos_setting_table","3");
INSERT INTO migrations VALUES("172","2024_05_07_132625_add_is_default_to_sms_templates_table","3");
INSERT INTO migrations VALUES("173","2024_05_08_112211_change_address_and_city_field_to_nullable_in_customers_table","3");
INSERT INTO migrations VALUES("174","2024_05_08_151050_add_is_default_ecommerce_columne_to_sms_templates_table","3");
INSERT INTO migrations VALUES("175","2024_05_20_182757_add_wholesale_price_to_products_table","3");
INSERT INTO migrations VALUES("176","2024_05_21_170500_add_is_sent_to_transfers_table","3");
INSERT INTO migrations VALUES("177","2024_06_04_225113_create_income_categories_table","3");
INSERT INTO migrations VALUES("178","2024_06_04_225128_create_incomes_table","3");
INSERT INTO migrations VALUES("179","2024_06_29_131917_add_is_packing_slip_to_general_settings_table","3");
INSERT INTO migrations VALUES("180","2024_07_05_192531_create_packing_slips_table","3");
INSERT INTO migrations VALUES("181","2024_07_05_193002_create_packing_slip_products_table","3");
INSERT INTO migrations VALUES("182","2024_07_05_194501_add_is_packing_and_delivered_to_product_sales_table","3");
INSERT INTO migrations VALUES("183","2024_07_14_122245_add_delivery_id_to_packing_slips_table","3");
INSERT INTO migrations VALUES("184","2024_07_14_122415_add_variant_id_to_packing_slip_products_table","3");
INSERT INTO migrations VALUES("185","2024_07_14_122519_add_packing_slip_ids_to_deliveries_table","3");
INSERT INTO migrations VALUES("186","2024_07_16_125908_create_challans_table","3");
INSERT INTO migrations VALUES("187","2024_08_12_112830_add_thermal_invoice_size_to_pos_setting","3");
INSERT INTO migrations VALUES("188","2024_08_14_133351_add_expiry_type_value_to_general_settings","3");
INSERT INTO migrations VALUES("189","2024_09_01_120515_create_productions_table","3");
INSERT INTO migrations VALUES("190","2024_09_01_120536_create_product_productions_table","3");
INSERT INTO migrations VALUES("191","2024_09_11_151744_add_return_qty_to_product_purchases_table","3");
INSERT INTO migrations VALUES("192","2024_09_12_162309_create_barcodes_table","3");
INSERT INTO migrations VALUES("193","2024_10_10_121312_add_data_to_payment_with_credit_card_table","3");
INSERT INTO migrations VALUES("194","2024_10_10_212501_alter_attendances_table","3");
INSERT INTO migrations VALUES("195","2024_10_10_213757_alter_attendances_table","3");
INSERT INTO migrations VALUES("196","2024_10_14_144917_change_column_to_nullable_to_payment_with_credit_card_table","3");
INSERT INTO migrations VALUES("197","2024_11_10_121521_add_code_and_type_to_accounts_table","3");
INSERT INTO migrations VALUES("198","2024_11_24_100926_add_module_status_to_external_services_table","3");



CREATE TABLE `money_transfers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_account_id` int NOT NULL,
  `to_account_id` int NOT NULL,
  `amount` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `notifications` (
  `id` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint unsigned NOT NULL,
  `data` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `packing_slip_products` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `packing_slip_id` int NOT NULL,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `packing_slips` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `sale_id` int NOT NULL,
  `delivery_id` int DEFAULT NULL,
  `amount` double NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `password_resets` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `payment_with_cheque` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` int NOT NULL,
  `cheque_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `payment_with_credit_card` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` int NOT NULL,
  `customer_id` int DEFAULT NULL,
  `customer_stripe_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `charge_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `payment_with_gift_card` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` int NOT NULL,
  `gift_card_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `payment_with_paypal` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `payment_id` int NOT NULL,
  `transaction_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `payments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `purchase_id` int DEFAULT NULL,
  `sale_id` int DEFAULT NULL,
  `cash_register_id` int DEFAULT NULL,
  `account_id` int NOT NULL,
  `payment_receiver` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_reference` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `amount` double NOT NULL,
  `change` double NOT NULL,
  `used_points` double DEFAULT NULL,
  `paying_method` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `payment_note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `payrolls` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `employee_id` int NOT NULL,
  `account_id` int NOT NULL,
  `user_id` int NOT NULL,
  `amount` double NOT NULL,
  `paying_method` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `permissions` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=127 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO permissions VALUES("4","products-edit","web","2018-06-03 03:15:09","2018-06-03 03:15:09");
INSERT INTO permissions VALUES("5","products-delete","web","2018-06-04 01:09:22","2018-06-04 01:09:22");
INSERT INTO permissions VALUES("6","products-add","web","2018-06-04 02:49:14","2018-06-04 02:49:14");
INSERT INTO permissions VALUES("7","products-index","web","2018-06-04 05:49:27","2018-06-04 05:49:27");
INSERT INTO permissions VALUES("8","purchases-index","web","2018-06-04 10:18:19","2018-06-04 10:18:19");
INSERT INTO permissions VALUES("9","purchases-add","web","2018-06-04 10:27:25","2018-06-04 10:27:25");
INSERT INTO permissions VALUES("10","purchases-edit","web","2018-06-04 12:02:36","2018-06-04 12:02:36");
INSERT INTO permissions VALUES("11","purchases-delete","web","2018-06-04 12:02:36","2018-06-04 12:02:36");
INSERT INTO permissions VALUES("12","sales-index","web","2018-06-04 13:04:08","2018-06-04 13:04:08");
INSERT INTO permissions VALUES("13","sales-add","web","2018-06-04 13:04:52","2018-06-04 13:04:52");
INSERT INTO permissions VALUES("14","sales-edit","web","2018-06-04 13:04:52","2018-06-04 13:04:52");
INSERT INTO permissions VALUES("15","sales-delete","web","2018-06-04 13:04:53","2018-06-04 13:04:53");
INSERT INTO permissions VALUES("16","quotes-index","web","2018-06-05 00:20:10","2018-06-05 00:20:10");
INSERT INTO permissions VALUES("17","quotes-add","web","2018-06-05 00:20:10","2018-06-05 00:20:10");
INSERT INTO permissions VALUES("18","quotes-edit","web","2018-06-05 00:20:10","2018-06-05 00:20:10");
INSERT INTO permissions VALUES("19","quotes-delete","web","2018-06-05 00:20:10","2018-06-05 00:20:10");
INSERT INTO permissions VALUES("20","transfers-index","web","2018-06-05 00:45:03","2018-06-05 00:45:03");
INSERT INTO permissions VALUES("21","transfers-add","web","2018-06-05 00:45:03","2018-06-05 00:45:03");
INSERT INTO permissions VALUES("22","transfers-edit","web","2018-06-05 00:45:03","2018-06-05 00:45:03");
INSERT INTO permissions VALUES("23","transfers-delete","web","2018-06-05 00:45:03","2018-06-05 00:45:03");
INSERT INTO permissions VALUES("24","returns-index","web","2018-06-05 01:05:24","2018-06-05 01:05:24");
INSERT INTO permissions VALUES("25","returns-add","web","2018-06-05 01:05:24","2018-06-05 01:05:24");
INSERT INTO permissions VALUES("26","returns-edit","web","2018-06-05 01:05:25","2018-06-05 01:05:25");
INSERT INTO permissions VALUES("27","returns-delete","web","2018-06-05 01:05:25","2018-06-05 01:05:25");
INSERT INTO permissions VALUES("28","customers-index","web","2018-06-05 01:30:54","2018-06-05 01:30:54");
INSERT INTO permissions VALUES("29","customers-add","web","2018-06-05 01:30:55","2018-06-05 01:30:55");
INSERT INTO permissions VALUES("30","customers-edit","web","2018-06-05 01:30:55","2018-06-05 01:30:55");
INSERT INTO permissions VALUES("31","customers-delete","web","2018-06-05 01:30:55","2018-06-05 01:30:55");
INSERT INTO permissions VALUES("32","suppliers-index","web","2018-06-05 01:55:12","2018-06-05 01:55:12");
INSERT INTO permissions VALUES("33","suppliers-add","web","2018-06-05 01:55:12","2018-06-05 01:55:12");
INSERT INTO permissions VALUES("34","suppliers-edit","web","2018-06-05 01:55:12","2018-06-05 01:55:12");
INSERT INTO permissions VALUES("35","suppliers-delete","web","2018-06-05 01:55:12","2018-06-05 01:55:12");
INSERT INTO permissions VALUES("36","product-report","web","2018-06-25 01:20:33","2018-06-25 01:20:33");
INSERT INTO permissions VALUES("37","purchase-report","web","2018-06-25 01:39:56","2018-06-25 01:39:56");
INSERT INTO permissions VALUES("38","sale-report","web","2018-06-25 01:48:13","2018-06-25 01:48:13");
INSERT INTO permissions VALUES("39","customer-report","web","2018-06-25 01:51:51","2018-06-25 01:51:51");
INSERT INTO permissions VALUES("40","due-report","web","2018-06-25 01:54:52","2018-06-25 01:54:52");
INSERT INTO permissions VALUES("41","users-index","web","2018-06-25 02:15:10","2018-06-25 02:15:10");
INSERT INTO permissions VALUES("42","users-add","web","2018-06-25 02:15:10","2018-06-25 02:15:10");
INSERT INTO permissions VALUES("43","users-edit","web","2018-06-25 02:16:30","2018-06-25 02:16:30");
INSERT INTO permissions VALUES("44","users-delete","web","2018-06-25 02:16:30","2018-06-25 02:16:30");
INSERT INTO permissions VALUES("45","profit-loss","web","2018-07-15 00:05:05","2018-07-15 00:05:05");
INSERT INTO permissions VALUES("46","best-seller","web","2018-07-15 00:16:38","2018-07-15 00:16:38");
INSERT INTO permissions VALUES("47","daily-sale","web","2018-07-15 00:39:21","2018-07-15 00:39:21");
INSERT INTO permissions VALUES("48","monthly-sale","web","2018-07-15 00:45:41","2018-07-15 00:45:41");
INSERT INTO permissions VALUES("49","daily-purchase","web","2018-07-15 00:51:46","2018-07-15 00:51:46");
INSERT INTO permissions VALUES("50","monthly-purchase","web","2018-07-15 01:03:17","2018-07-15 01:03:17");
INSERT INTO permissions VALUES("51","payment-report","web","2018-07-15 01:25:41","2018-07-15 01:25:41");
INSERT INTO permissions VALUES("52","warehouse-stock-report","web","2018-07-15 01:31:55","2018-07-15 01:31:55");
INSERT INTO permissions VALUES("53","product-qty-alert","web","2018-07-15 01:48:21","2018-07-15 01:48:21");
INSERT INTO permissions VALUES("54","supplier-report","web","2018-07-30 05:15:01","2018-07-30 05:15:01");
INSERT INTO permissions VALUES("55","expenses-index","web","2018-09-05 03:22:10","2018-09-05 03:22:10");
INSERT INTO permissions VALUES("56","expenses-add","web","2018-09-05 03:22:10","2018-09-05 03:22:10");
INSERT INTO permissions VALUES("57","expenses-edit","web","2018-09-05 03:22:10","2018-09-05 03:22:10");
INSERT INTO permissions VALUES("58","expenses-delete","web","2018-09-05 03:22:11","2018-09-05 03:22:11");
INSERT INTO permissions VALUES("59","general_setting","web","2018-10-20 01:25:04","2018-10-20 01:25:04");
INSERT INTO permissions VALUES("60","mail_setting","web","2018-10-20 01:25:04","2018-10-20 01:25:04");
INSERT INTO permissions VALUES("61","pos_setting","web","2018-10-20 01:25:04","2018-10-20 01:25:04");
INSERT INTO permissions VALUES("62","hrm_setting","web","2019-01-02 12:45:23","2019-01-02 12:45:23");
INSERT INTO permissions VALUES("63","purchase-return-index","web","2019-01-03 00:00:14","2019-01-03 00:00:14");
INSERT INTO permissions VALUES("64","purchase-return-add","web","2019-01-03 00:00:14","2019-01-03 00:00:14");
INSERT INTO permissions VALUES("65","purchase-return-edit","web","2019-01-03 00:00:14","2019-01-03 00:00:14");
INSERT INTO permissions VALUES("66","purchase-return-delete","web","2019-01-03 00:00:14","2019-01-03 00:00:14");
INSERT INTO permissions VALUES("67","account-index","web","2019-01-03 00:21:13","2019-01-03 00:21:13");
INSERT INTO permissions VALUES("68","balance-sheet","web","2019-01-03 00:21:14","2019-01-03 00:21:14");
INSERT INTO permissions VALUES("69","account-statement","web","2019-01-03 00:21:14","2019-01-03 00:21:14");
INSERT INTO permissions VALUES("70","department","web","2019-01-03 00:45:01","2019-01-03 00:45:01");
INSERT INTO permissions VALUES("71","attendance","web","2019-01-03 00:45:01","2019-01-03 00:45:01");
INSERT INTO permissions VALUES("72","payroll","web","2019-01-03 00:45:01","2019-01-03 00:45:01");
INSERT INTO permissions VALUES("73","employees-index","web","2019-01-03 01:07:19","2019-01-03 01:07:19");
INSERT INTO permissions VALUES("74","employees-add","web","2019-01-03 01:07:19","2019-01-03 01:07:19");
INSERT INTO permissions VALUES("75","employees-edit","web","2019-01-03 01:07:19","2019-01-03 01:07:19");
INSERT INTO permissions VALUES("76","employees-delete","web","2019-01-03 01:07:19","2019-01-03 01:07:19");
INSERT INTO permissions VALUES("77","user-report","web","2019-01-16 09:03:18","2019-01-16 09:03:18");
INSERT INTO permissions VALUES("78","stock_count","web","2019-02-17 12:47:01","2019-02-17 12:47:01");
INSERT INTO permissions VALUES("79","adjustment","web","2019-02-17 12:47:02","2019-02-17 12:47:02");
INSERT INTO permissions VALUES("80","sms_setting","web","2019-02-22 07:33:03","2019-02-22 07:33:03");
INSERT INTO permissions VALUES("81","create_sms","web","2019-02-22 07:33:03","2019-02-22 07:33:03");
INSERT INTO permissions VALUES("82","print_barcode","web","2019-03-07 07:17:19","2019-03-07 07:17:19");
INSERT INTO permissions VALUES("83","empty_database","web","2019-03-07 07:17:19","2019-03-07 07:17:19");
INSERT INTO permissions VALUES("84","customer_group","web","2019-03-07 07:52:15","2019-03-07 07:52:15");
INSERT INTO permissions VALUES("85","unit","web","2019-03-07 07:52:15","2019-03-07 07:52:15");
INSERT INTO permissions VALUES("86","tax","web","2019-03-07 07:52:15","2019-03-07 07:52:15");
INSERT INTO permissions VALUES("87","gift_card","web","2019-03-07 08:44:38","2019-03-07 08:44:38");
INSERT INTO permissions VALUES("88","coupon","web","2019-03-07 08:44:38","2019-03-07 08:44:38");
INSERT INTO permissions VALUES("89","holiday","web","2019-10-19 11:12:15","2019-10-19 11:12:15");
INSERT INTO permissions VALUES("90","warehouse-report","web","2019-10-22 08:15:23","2019-10-22 08:15:23");
INSERT INTO permissions VALUES("91","warehouse","web","2020-02-26 09:02:32","2020-02-26 09:02:32");
INSERT INTO permissions VALUES("92","brand","web","2020-02-26 09:14:59","2020-02-26 09:14:59");
INSERT INTO permissions VALUES("93","billers-index","web","2020-02-26 09:26:15","2020-02-26 09:26:15");
INSERT INTO permissions VALUES("94","billers-add","web","2020-02-26 09:26:15","2020-02-26 09:26:15");
INSERT INTO permissions VALUES("95","billers-edit","web","2020-02-26 09:26:15","2020-02-26 09:26:15");
INSERT INTO permissions VALUES("96","billers-delete","web","2020-02-26 09:26:15","2020-02-26 09:26:15");
INSERT INTO permissions VALUES("97","money-transfer","web","2020-03-02 07:56:48","2020-03-02 07:56:48");
INSERT INTO permissions VALUES("98","category","web","2020-07-13 14:28:16","2020-07-13 14:28:16");
INSERT INTO permissions VALUES("99","delivery","web","2020-07-13 14:28:16","2020-07-13 14:28:16");
INSERT INTO permissions VALUES("100","send_notification","web","2020-10-31 08:36:31","2020-10-31 08:36:31");
INSERT INTO permissions VALUES("101","today_sale","web","2020-10-31 09:12:04","2020-10-31 09:12:04");
INSERT INTO permissions VALUES("102","today_profit","web","2020-10-31 09:12:04","2020-10-31 09:12:04");
INSERT INTO permissions VALUES("103","currency","web","2020-11-09 02:38:11","2020-11-09 02:38:11");
INSERT INTO permissions VALUES("104","backup_database","web","2020-11-15 02:31:55","2020-11-15 02:31:55");
INSERT INTO permissions VALUES("105","reward_point_setting","web","2021-06-27 06:49:42","2021-06-27 06:49:42");
INSERT INTO permissions VALUES("106","revenue_profit_summary","web","2022-02-08 16:12:21","2022-02-08 16:12:21");
INSERT INTO permissions VALUES("107","cash_flow","web","2022-02-08 16:12:22","2022-02-08 16:12:22");
INSERT INTO permissions VALUES("108","monthly_summary","web","2022-02-08 16:12:22","2022-02-08 16:12:22");
INSERT INTO permissions VALUES("109","yearly_report","web","2022-02-08 16:12:22","2022-02-08 16:12:22");
INSERT INTO permissions VALUES("110","discount_plan","web","2022-02-16 11:27:26","2022-02-16 11:27:26");
INSERT INTO permissions VALUES("111","discount","web","2022-02-16 11:27:38","2022-02-16 11:27:38");
INSERT INTO permissions VALUES("112","product-expiry-report","web","2022-03-30 07:54:20","2022-03-30 07:54:20");
INSERT INTO permissions VALUES("113","purchase-payment-index","web","2022-06-05 16:27:27","2022-06-05 16:27:27");
INSERT INTO permissions VALUES("114","purchase-payment-add","web","2022-06-05 16:27:28","2022-06-05 16:27:28");
INSERT INTO permissions VALUES("115","purchase-payment-edit","web","2022-06-05 16:27:28","2022-06-05 16:27:28");
INSERT INTO permissions VALUES("116","purchase-payment-delete","web","2022-06-05 16:27:28","2022-06-05 16:27:28");
INSERT INTO permissions VALUES("117","sale-payment-index","web","2022-06-05 16:27:28","2022-06-05 16:27:28");
INSERT INTO permissions VALUES("118","sale-payment-add","web","2022-06-05 16:27:28","2022-06-05 16:27:28");
INSERT INTO permissions VALUES("119","sale-payment-edit","web","2022-06-05 16:27:28","2022-06-05 16:27:28");
INSERT INTO permissions VALUES("120","sale-payment-delete","web","2022-06-05 16:27:28","2022-06-05 16:27:28");
INSERT INTO permissions VALUES("121","all_notification","web","2022-06-05 16:27:29","2022-06-05 16:27:29");
INSERT INTO permissions VALUES("122","sale-report-chart","web","2022-06-05 16:27:29","2022-06-05 16:27:29");
INSERT INTO permissions VALUES("123","dso-report","web","2022-06-05 16:27:29","2022-06-05 16:27:29");
INSERT INTO permissions VALUES("124","product_history","web","2022-08-25 16:19:05","2022-08-25 16:19:05");
INSERT INTO permissions VALUES("125","supplier-due-report","web","2022-08-31 12:01:33","2022-08-31 12:01:33");
INSERT INTO permissions VALUES("126","custom_field","web","2023-05-02 09:56:35","2023-05-02 09:56:35");



CREATE TABLE `pos_setting` (
  `id` int NOT NULL,
  `customer_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `biller_id` int NOT NULL,
  `product_number` int NOT NULL,
  `keybord_active` tinyint(1) NOT NULL,
  `is_table` tinyint(1) NOT NULL DEFAULT '0',
  `send_sms` tinyint(1) NOT NULL DEFAULT '0',
  `stripe_public_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `stripe_secret_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paypal_live_api_username` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paypal_live_api_password` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paypal_live_api_secret` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_options` text COLLATE utf8mb4_unicode_ci,
  `invoice_option` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `thermal_invoice_size` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '80',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  UNIQUE KEY `pos_setting_id_unique` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO pos_setting VALUES("1","1","1","1","50","0","0","0","","","","","","cash,card,gift_card,deposit","thermal","80","2023-06-21 16:35:02","2023-09-17 02:21:23");



CREATE TABLE `product_adjustments` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `adjustment_id` int NOT NULL,
  `product_id` int NOT NULL,
  `variant_id` int DEFAULT NULL,
  `unit_cost` double DEFAULT NULL,
  `qty` double NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_batches` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `batch_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expired_date` date NOT NULL,
  `qty` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_productions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `production_id` int NOT NULL,
  `product_id` int NOT NULL,
  `qty` double NOT NULL,
  `recieved` double NOT NULL,
  `purchase_unit_id` int NOT NULL,
  `net_unit_cost` double NOT NULL,
  `tax_rate` double NOT NULL,
  `tax` double NOT NULL,
  `total` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_purchases` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `purchase_id` int NOT NULL,
  `product_id` int NOT NULL,
  `product_batch_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `imei_number` text COLLATE utf8mb4_unicode_ci,
  `qty` double NOT NULL,
  `recieved` double NOT NULL,
  `return_qty` double NOT NULL DEFAULT '0',
  `purchase_unit_id` int NOT NULL,
  `net_unit_cost` double NOT NULL,
  `discount` double NOT NULL,
  `tax_rate` double NOT NULL,
  `tax` double NOT NULL,
  `total` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_quotation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `quotation_id` int NOT NULL,
  `product_id` int NOT NULL,
  `product_batch_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `qty` double NOT NULL,
  `sale_unit_id` int NOT NULL,
  `net_unit_price` double NOT NULL,
  `discount` double NOT NULL,
  `tax_rate` double NOT NULL,
  `tax` double NOT NULL,
  `total` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_returns` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `return_id` int NOT NULL,
  `product_id` int NOT NULL,
  `product_batch_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `imei_number` text COLLATE utf8mb4_unicode_ci,
  `qty` double NOT NULL,
  `sale_unit_id` int NOT NULL,
  `net_unit_price` double NOT NULL,
  `discount` double NOT NULL,
  `tax_rate` double NOT NULL,
  `tax` double NOT NULL,
  `total` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_sales` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `sale_id` int NOT NULL,
  `product_id` int NOT NULL,
  `product_batch_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `imei_number` text COLLATE utf8mb4_unicode_ci,
  `qty` double NOT NULL,
  `return_qty` double NOT NULL DEFAULT '0',
  `sale_unit_id` int NOT NULL,
  `net_unit_price` double NOT NULL,
  `discount` double NOT NULL,
  `tax_rate` double NOT NULL,
  `tax` double NOT NULL,
  `total` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_delivered` tinyint(1) DEFAULT NULL,
  `is_packing` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_transfer` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `transfer_id` int NOT NULL,
  `product_id` int NOT NULL,
  `product_batch_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `imei_number` text COLLATE utf8mb4_unicode_ci,
  `qty` double NOT NULL,
  `purchase_unit_id` int NOT NULL,
  `net_unit_cost` double NOT NULL,
  `tax_rate` double NOT NULL,
  `tax` double NOT NULL,
  `total` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_variants` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `product_id` int NOT NULL,
  `variant_id` int NOT NULL,
  `position` int NOT NULL,
  `item_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `additional_cost` double DEFAULT NULL,
  `additional_price` double DEFAULT NULL,
  `qty` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `product_warehouse` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `product_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_batch_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `imei_number` text COLLATE utf8mb4_unicode_ci,
  `warehouse_id` int NOT NULL,
  `qty` double NOT NULL,
  `price` double DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `productions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `warehouse_id` int NOT NULL,
  `user_id` int NOT NULL,
  `item` int NOT NULL,
  `total_qty` int NOT NULL,
  `total_tax` double NOT NULL,
  `total_cost` double NOT NULL,
  `shipping_cost` double DEFAULT NULL,
  `grand_total` double NOT NULL,
  `status` int NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `products` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `barcode_symbology` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `brand_id` int DEFAULT NULL,
  `category_id` int NOT NULL,
  `unit_id` int NOT NULL,
  `purchase_unit_id` int NOT NULL,
  `sale_unit_id` int NOT NULL,
  `cost` double NOT NULL,
  `price` double NOT NULL,
  `wholesale_price` double DEFAULT NULL,
  `qty` double DEFAULT NULL,
  `alert_quantity` double DEFAULT NULL,
  `daily_sale_objective` double DEFAULT NULL,
  `promotion` tinyint DEFAULT NULL,
  `promotion_price` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `starting_date` date DEFAULT NULL,
  `last_date` date DEFAULT NULL,
  `tax_id` int DEFAULT NULL,
  `tax_method` int DEFAULT NULL,
  `image` longtext COLLATE utf8mb4_unicode_ci,
  `file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_embeded` tinyint(1) DEFAULT NULL,
  `is_variant` tinyint(1) DEFAULT NULL,
  `is_batch` tinyint(1) DEFAULT NULL,
  `is_diffPrice` tinyint(1) DEFAULT NULL,
  `is_imei` tinyint(1) DEFAULT NULL,
  `featured` tinyint DEFAULT NULL,
  `product_list` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `variant_list` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `qty_list` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `price_list` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product_details` text COLLATE utf8mb4_unicode_ci,
  `variant_option` text COLLATE utf8mb4_unicode_ci,
  `variant_value` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `purchase_product_return` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `return_id` int NOT NULL,
  `product_id` int NOT NULL,
  `product_batch_id` int DEFAULT NULL,
  `variant_id` int DEFAULT NULL,
  `imei_number` text COLLATE utf8mb4_unicode_ci,
  `qty` double NOT NULL,
  `purchase_unit_id` int NOT NULL,
  `net_unit_cost` double NOT NULL,
  `discount` double NOT NULL,
  `tax_rate` double NOT NULL,
  `tax` double NOT NULL,
  `total` double NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `purchases` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `supplier_id` int DEFAULT NULL,
  `currency_id` int DEFAULT NULL,
  `exchange_rate` double DEFAULT NULL,
  `item` int NOT NULL,
  `total_qty` int NOT NULL,
  `total_discount` double NOT NULL,
  `total_tax` double NOT NULL,
  `total_cost` double NOT NULL,
  `order_tax_rate` double DEFAULT NULL,
  `order_tax` double DEFAULT NULL,
  `order_discount` double DEFAULT NULL,
  `shipping_cost` double DEFAULT NULL,
  `grand_total` double NOT NULL,
  `paid_amount` double NOT NULL,
  `status` int NOT NULL,
  `payment_status` int NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `quotations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `biller_id` int NOT NULL,
  `supplier_id` int DEFAULT NULL,
  `customer_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `item` int NOT NULL,
  `total_qty` double NOT NULL,
  `total_discount` double NOT NULL,
  `total_tax` double NOT NULL,
  `total_price` double NOT NULL,
  `order_tax_rate` double DEFAULT NULL,
  `order_tax` double DEFAULT NULL,
  `order_discount` double DEFAULT NULL,
  `shipping_cost` double DEFAULT NULL,
  `grand_total` double NOT NULL,
  `quotation_status` int NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `return_purchases` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `supplier_id` int DEFAULT NULL,
  `warehouse_id` int NOT NULL,
  `user_id` int NOT NULL,
  `purchase_id` int DEFAULT NULL,
  `account_id` int NOT NULL,
  `currency_id` int DEFAULT NULL,
  `exchange_rate` double DEFAULT NULL,
  `item` int NOT NULL,
  `total_qty` double NOT NULL,
  `total_discount` double NOT NULL,
  `total_tax` double NOT NULL,
  `total_cost` double NOT NULL,
  `order_tax_rate` double DEFAULT NULL,
  `order_tax` double DEFAULT NULL,
  `grand_total` double NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `return_note` text COLLATE utf8mb4_unicode_ci,
  `staff_note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `returns` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `sale_id` int DEFAULT NULL,
  `cash_register_id` int DEFAULT NULL,
  `customer_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `biller_id` int NOT NULL,
  `account_id` int NOT NULL,
  `currency_id` int DEFAULT NULL,
  `exchange_rate` double DEFAULT NULL,
  `item` int NOT NULL,
  `total_qty` double NOT NULL,
  `total_discount` double NOT NULL,
  `total_tax` double NOT NULL,
  `total_price` double NOT NULL,
  `order_tax_rate` double DEFAULT NULL,
  `order_tax` double DEFAULT NULL,
  `grand_total` double NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `return_note` text COLLATE utf8mb4_unicode_ci,
  `staff_note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `reward_point_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `per_point_amount` double NOT NULL,
  `minimum_amount` double NOT NULL,
  `duration` int DEFAULT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO reward_point_settings VALUES("1","500","0","0","Year","0","2023-09-17 02:13:38","2023-09-17 02:13:38");



CREATE TABLE `role_has_permissions` (
  `permission_id` int unsigned NOT NULL,
  `role_id` int unsigned NOT NULL,
  PRIMARY KEY (`permission_id`,`role_id`),
  KEY `role_has_permissions_role_id_foreign` (`role_id`),
  CONSTRAINT `role_has_permissions_permission_id_foreign` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_has_permissions_role_id_foreign` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO role_has_permissions VALUES("4","1");
INSERT INTO role_has_permissions VALUES("5","1");
INSERT INTO role_has_permissions VALUES("6","1");
INSERT INTO role_has_permissions VALUES("7","1");
INSERT INTO role_has_permissions VALUES("8","1");
INSERT INTO role_has_permissions VALUES("9","1");
INSERT INTO role_has_permissions VALUES("10","1");
INSERT INTO role_has_permissions VALUES("11","1");
INSERT INTO role_has_permissions VALUES("12","1");
INSERT INTO role_has_permissions VALUES("13","1");
INSERT INTO role_has_permissions VALUES("14","1");
INSERT INTO role_has_permissions VALUES("15","1");
INSERT INTO role_has_permissions VALUES("16","1");
INSERT INTO role_has_permissions VALUES("17","1");
INSERT INTO role_has_permissions VALUES("18","1");
INSERT INTO role_has_permissions VALUES("19","1");
INSERT INTO role_has_permissions VALUES("20","1");
INSERT INTO role_has_permissions VALUES("21","1");
INSERT INTO role_has_permissions VALUES("22","1");
INSERT INTO role_has_permissions VALUES("23","1");
INSERT INTO role_has_permissions VALUES("24","1");
INSERT INTO role_has_permissions VALUES("25","1");
INSERT INTO role_has_permissions VALUES("26","1");
INSERT INTO role_has_permissions VALUES("27","1");
INSERT INTO role_has_permissions VALUES("28","1");
INSERT INTO role_has_permissions VALUES("29","1");
INSERT INTO role_has_permissions VALUES("30","1");
INSERT INTO role_has_permissions VALUES("31","1");
INSERT INTO role_has_permissions VALUES("32","1");
INSERT INTO role_has_permissions VALUES("33","1");
INSERT INTO role_has_permissions VALUES("34","1");
INSERT INTO role_has_permissions VALUES("35","1");
INSERT INTO role_has_permissions VALUES("36","1");
INSERT INTO role_has_permissions VALUES("37","1");
INSERT INTO role_has_permissions VALUES("38","1");
INSERT INTO role_has_permissions VALUES("39","1");
INSERT INTO role_has_permissions VALUES("40","1");
INSERT INTO role_has_permissions VALUES("41","1");
INSERT INTO role_has_permissions VALUES("42","1");
INSERT INTO role_has_permissions VALUES("43","1");
INSERT INTO role_has_permissions VALUES("44","1");
INSERT INTO role_has_permissions VALUES("45","1");
INSERT INTO role_has_permissions VALUES("46","1");
INSERT INTO role_has_permissions VALUES("47","1");
INSERT INTO role_has_permissions VALUES("48","1");
INSERT INTO role_has_permissions VALUES("49","1");
INSERT INTO role_has_permissions VALUES("50","1");
INSERT INTO role_has_permissions VALUES("51","1");
INSERT INTO role_has_permissions VALUES("52","1");
INSERT INTO role_has_permissions VALUES("53","1");
INSERT INTO role_has_permissions VALUES("54","1");
INSERT INTO role_has_permissions VALUES("55","1");
INSERT INTO role_has_permissions VALUES("56","1");
INSERT INTO role_has_permissions VALUES("57","1");
INSERT INTO role_has_permissions VALUES("58","1");
INSERT INTO role_has_permissions VALUES("59","1");
INSERT INTO role_has_permissions VALUES("60","1");
INSERT INTO role_has_permissions VALUES("61","1");
INSERT INTO role_has_permissions VALUES("62","1");
INSERT INTO role_has_permissions VALUES("63","1");
INSERT INTO role_has_permissions VALUES("64","1");
INSERT INTO role_has_permissions VALUES("65","1");
INSERT INTO role_has_permissions VALUES("66","1");
INSERT INTO role_has_permissions VALUES("67","1");
INSERT INTO role_has_permissions VALUES("68","1");
INSERT INTO role_has_permissions VALUES("69","1");
INSERT INTO role_has_permissions VALUES("70","1");
INSERT INTO role_has_permissions VALUES("71","1");
INSERT INTO role_has_permissions VALUES("72","1");
INSERT INTO role_has_permissions VALUES("73","1");
INSERT INTO role_has_permissions VALUES("74","1");
INSERT INTO role_has_permissions VALUES("75","1");
INSERT INTO role_has_permissions VALUES("76","1");
INSERT INTO role_has_permissions VALUES("77","1");
INSERT INTO role_has_permissions VALUES("78","1");
INSERT INTO role_has_permissions VALUES("79","1");
INSERT INTO role_has_permissions VALUES("80","1");
INSERT INTO role_has_permissions VALUES("81","1");
INSERT INTO role_has_permissions VALUES("82","1");
INSERT INTO role_has_permissions VALUES("83","1");
INSERT INTO role_has_permissions VALUES("84","1");
INSERT INTO role_has_permissions VALUES("85","1");
INSERT INTO role_has_permissions VALUES("86","1");
INSERT INTO role_has_permissions VALUES("87","1");
INSERT INTO role_has_permissions VALUES("88","1");
INSERT INTO role_has_permissions VALUES("89","1");
INSERT INTO role_has_permissions VALUES("90","1");
INSERT INTO role_has_permissions VALUES("91","1");
INSERT INTO role_has_permissions VALUES("92","1");
INSERT INTO role_has_permissions VALUES("93","1");
INSERT INTO role_has_permissions VALUES("94","1");
INSERT INTO role_has_permissions VALUES("95","1");
INSERT INTO role_has_permissions VALUES("96","1");
INSERT INTO role_has_permissions VALUES("97","1");
INSERT INTO role_has_permissions VALUES("98","1");
INSERT INTO role_has_permissions VALUES("99","1");
INSERT INTO role_has_permissions VALUES("100","1");
INSERT INTO role_has_permissions VALUES("101","1");
INSERT INTO role_has_permissions VALUES("102","1");
INSERT INTO role_has_permissions VALUES("103","1");
INSERT INTO role_has_permissions VALUES("104","1");
INSERT INTO role_has_permissions VALUES("105","1");
INSERT INTO role_has_permissions VALUES("106","1");
INSERT INTO role_has_permissions VALUES("107","1");
INSERT INTO role_has_permissions VALUES("108","1");
INSERT INTO role_has_permissions VALUES("109","1");
INSERT INTO role_has_permissions VALUES("110","1");
INSERT INTO role_has_permissions VALUES("111","1");
INSERT INTO role_has_permissions VALUES("112","1");
INSERT INTO role_has_permissions VALUES("113","1");
INSERT INTO role_has_permissions VALUES("114","1");
INSERT INTO role_has_permissions VALUES("115","1");
INSERT INTO role_has_permissions VALUES("116","1");
INSERT INTO role_has_permissions VALUES("117","1");
INSERT INTO role_has_permissions VALUES("118","1");
INSERT INTO role_has_permissions VALUES("119","1");
INSERT INTO role_has_permissions VALUES("120","1");
INSERT INTO role_has_permissions VALUES("121","1");
INSERT INTO role_has_permissions VALUES("122","1");
INSERT INTO role_has_permissions VALUES("123","1");
INSERT INTO role_has_permissions VALUES("124","1");
INSERT INTO role_has_permissions VALUES("125","1");
INSERT INTO role_has_permissions VALUES("126","1");
INSERT INTO role_has_permissions VALUES("4","2");
INSERT INTO role_has_permissions VALUES("5","2");
INSERT INTO role_has_permissions VALUES("6","2");
INSERT INTO role_has_permissions VALUES("7","2");
INSERT INTO role_has_permissions VALUES("8","2");
INSERT INTO role_has_permissions VALUES("9","2");
INSERT INTO role_has_permissions VALUES("10","2");
INSERT INTO role_has_permissions VALUES("11","2");
INSERT INTO role_has_permissions VALUES("12","2");
INSERT INTO role_has_permissions VALUES("13","2");
INSERT INTO role_has_permissions VALUES("14","2");
INSERT INTO role_has_permissions VALUES("15","2");
INSERT INTO role_has_permissions VALUES("16","2");
INSERT INTO role_has_permissions VALUES("17","2");
INSERT INTO role_has_permissions VALUES("18","2");
INSERT INTO role_has_permissions VALUES("19","2");
INSERT INTO role_has_permissions VALUES("20","2");
INSERT INTO role_has_permissions VALUES("21","2");
INSERT INTO role_has_permissions VALUES("22","2");
INSERT INTO role_has_permissions VALUES("23","2");
INSERT INTO role_has_permissions VALUES("24","2");
INSERT INTO role_has_permissions VALUES("25","2");
INSERT INTO role_has_permissions VALUES("26","2");
INSERT INTO role_has_permissions VALUES("27","2");
INSERT INTO role_has_permissions VALUES("28","2");
INSERT INTO role_has_permissions VALUES("29","2");
INSERT INTO role_has_permissions VALUES("30","2");
INSERT INTO role_has_permissions VALUES("31","2");
INSERT INTO role_has_permissions VALUES("32","2");
INSERT INTO role_has_permissions VALUES("33","2");
INSERT INTO role_has_permissions VALUES("34","2");
INSERT INTO role_has_permissions VALUES("35","2");
INSERT INTO role_has_permissions VALUES("36","2");
INSERT INTO role_has_permissions VALUES("37","2");
INSERT INTO role_has_permissions VALUES("38","2");
INSERT INTO role_has_permissions VALUES("39","2");
INSERT INTO role_has_permissions VALUES("40","2");
INSERT INTO role_has_permissions VALUES("41","2");
INSERT INTO role_has_permissions VALUES("42","2");
INSERT INTO role_has_permissions VALUES("43","2");
INSERT INTO role_has_permissions VALUES("44","2");
INSERT INTO role_has_permissions VALUES("45","2");
INSERT INTO role_has_permissions VALUES("46","2");
INSERT INTO role_has_permissions VALUES("47","2");
INSERT INTO role_has_permissions VALUES("48","2");
INSERT INTO role_has_permissions VALUES("49","2");
INSERT INTO role_has_permissions VALUES("50","2");
INSERT INTO role_has_permissions VALUES("51","2");
INSERT INTO role_has_permissions VALUES("52","2");
INSERT INTO role_has_permissions VALUES("53","2");
INSERT INTO role_has_permissions VALUES("54","2");
INSERT INTO role_has_permissions VALUES("55","2");
INSERT INTO role_has_permissions VALUES("56","2");
INSERT INTO role_has_permissions VALUES("57","2");
INSERT INTO role_has_permissions VALUES("58","2");
INSERT INTO role_has_permissions VALUES("59","2");
INSERT INTO role_has_permissions VALUES("60","2");
INSERT INTO role_has_permissions VALUES("61","2");
INSERT INTO role_has_permissions VALUES("62","2");
INSERT INTO role_has_permissions VALUES("63","2");
INSERT INTO role_has_permissions VALUES("64","2");
INSERT INTO role_has_permissions VALUES("65","2");
INSERT INTO role_has_permissions VALUES("66","2");
INSERT INTO role_has_permissions VALUES("67","2");
INSERT INTO role_has_permissions VALUES("68","2");
INSERT INTO role_has_permissions VALUES("69","2");
INSERT INTO role_has_permissions VALUES("70","2");
INSERT INTO role_has_permissions VALUES("71","2");
INSERT INTO role_has_permissions VALUES("72","2");
INSERT INTO role_has_permissions VALUES("73","2");
INSERT INTO role_has_permissions VALUES("74","2");
INSERT INTO role_has_permissions VALUES("75","2");
INSERT INTO role_has_permissions VALUES("76","2");
INSERT INTO role_has_permissions VALUES("77","2");
INSERT INTO role_has_permissions VALUES("78","2");
INSERT INTO role_has_permissions VALUES("79","2");
INSERT INTO role_has_permissions VALUES("80","2");
INSERT INTO role_has_permissions VALUES("81","2");
INSERT INTO role_has_permissions VALUES("82","2");
INSERT INTO role_has_permissions VALUES("84","2");
INSERT INTO role_has_permissions VALUES("85","2");
INSERT INTO role_has_permissions VALUES("86","2");
INSERT INTO role_has_permissions VALUES("87","2");
INSERT INTO role_has_permissions VALUES("88","2");
INSERT INTO role_has_permissions VALUES("89","2");
INSERT INTO role_has_permissions VALUES("90","2");
INSERT INTO role_has_permissions VALUES("91","2");
INSERT INTO role_has_permissions VALUES("92","2");
INSERT INTO role_has_permissions VALUES("93","2");
INSERT INTO role_has_permissions VALUES("94","2");
INSERT INTO role_has_permissions VALUES("95","2");
INSERT INTO role_has_permissions VALUES("96","2");
INSERT INTO role_has_permissions VALUES("97","2");
INSERT INTO role_has_permissions VALUES("98","2");
INSERT INTO role_has_permissions VALUES("99","2");
INSERT INTO role_has_permissions VALUES("100","2");
INSERT INTO role_has_permissions VALUES("101","2");
INSERT INTO role_has_permissions VALUES("102","2");
INSERT INTO role_has_permissions VALUES("103","2");
INSERT INTO role_has_permissions VALUES("104","2");
INSERT INTO role_has_permissions VALUES("105","2");
INSERT INTO role_has_permissions VALUES("106","2");
INSERT INTO role_has_permissions VALUES("107","2");
INSERT INTO role_has_permissions VALUES("108","2");
INSERT INTO role_has_permissions VALUES("109","2");
INSERT INTO role_has_permissions VALUES("110","2");
INSERT INTO role_has_permissions VALUES("111","2");
INSERT INTO role_has_permissions VALUES("112","2");
INSERT INTO role_has_permissions VALUES("113","2");
INSERT INTO role_has_permissions VALUES("114","2");
INSERT INTO role_has_permissions VALUES("115","2");
INSERT INTO role_has_permissions VALUES("116","2");
INSERT INTO role_has_permissions VALUES("117","2");
INSERT INTO role_has_permissions VALUES("118","2");
INSERT INTO role_has_permissions VALUES("119","2");
INSERT INTO role_has_permissions VALUES("120","2");
INSERT INTO role_has_permissions VALUES("121","2");
INSERT INTO role_has_permissions VALUES("122","2");
INSERT INTO role_has_permissions VALUES("123","2");
INSERT INTO role_has_permissions VALUES("124","2");
INSERT INTO role_has_permissions VALUES("125","2");
INSERT INTO role_has_permissions VALUES("126","2");
INSERT INTO role_has_permissions VALUES("4","3");
INSERT INTO role_has_permissions VALUES("6","3");
INSERT INTO role_has_permissions VALUES("7","3");
INSERT INTO role_has_permissions VALUES("8","3");
INSERT INTO role_has_permissions VALUES("9","3");
INSERT INTO role_has_permissions VALUES("12","3");
INSERT INTO role_has_permissions VALUES("13","3");
INSERT INTO role_has_permissions VALUES("16","3");
INSERT INTO role_has_permissions VALUES("17","3");
INSERT INTO role_has_permissions VALUES("20","3");
INSERT INTO role_has_permissions VALUES("21","3");
INSERT INTO role_has_permissions VALUES("22","3");
INSERT INTO role_has_permissions VALUES("24","3");
INSERT INTO role_has_permissions VALUES("25","3");
INSERT INTO role_has_permissions VALUES("28","3");
INSERT INTO role_has_permissions VALUES("29","3");
INSERT INTO role_has_permissions VALUES("32","3");
INSERT INTO role_has_permissions VALUES("36","3");
INSERT INTO role_has_permissions VALUES("47","3");
INSERT INTO role_has_permissions VALUES("49","3");
INSERT INTO role_has_permissions VALUES("52","3");
INSERT INTO role_has_permissions VALUES("53","3");
INSERT INTO role_has_permissions VALUES("55","3");
INSERT INTO role_has_permissions VALUES("56","3");
INSERT INTO role_has_permissions VALUES("57","3");
INSERT INTO role_has_permissions VALUES("63","3");
INSERT INTO role_has_permissions VALUES("64","3");
INSERT INTO role_has_permissions VALUES("90","3");
INSERT INTO role_has_permissions VALUES("93","3");
INSERT INTO role_has_permissions VALUES("106","3");
INSERT INTO role_has_permissions VALUES("112","3");
INSERT INTO role_has_permissions VALUES("7","5");
INSERT INTO role_has_permissions VALUES("28","5");
INSERT INTO role_has_permissions VALUES("4","6");
INSERT INTO role_has_permissions VALUES("5","6");
INSERT INTO role_has_permissions VALUES("6","6");
INSERT INTO role_has_permissions VALUES("7","6");
INSERT INTO role_has_permissions VALUES("8","6");
INSERT INTO role_has_permissions VALUES("9","6");
INSERT INTO role_has_permissions VALUES("10","6");
INSERT INTO role_has_permissions VALUES("11","6");
INSERT INTO role_has_permissions VALUES("12","6");
INSERT INTO role_has_permissions VALUES("13","6");
INSERT INTO role_has_permissions VALUES("14","6");
INSERT INTO role_has_permissions VALUES("15","6");
INSERT INTO role_has_permissions VALUES("16","6");
INSERT INTO role_has_permissions VALUES("17","6");
INSERT INTO role_has_permissions VALUES("18","6");
INSERT INTO role_has_permissions VALUES("19","6");
INSERT INTO role_has_permissions VALUES("20","6");
INSERT INTO role_has_permissions VALUES("21","6");
INSERT INTO role_has_permissions VALUES("22","6");
INSERT INTO role_has_permissions VALUES("23","6");
INSERT INTO role_has_permissions VALUES("24","6");
INSERT INTO role_has_permissions VALUES("25","6");
INSERT INTO role_has_permissions VALUES("26","6");
INSERT INTO role_has_permissions VALUES("27","6");
INSERT INTO role_has_permissions VALUES("28","6");
INSERT INTO role_has_permissions VALUES("29","6");
INSERT INTO role_has_permissions VALUES("30","6");
INSERT INTO role_has_permissions VALUES("31","6");
INSERT INTO role_has_permissions VALUES("32","6");
INSERT INTO role_has_permissions VALUES("33","6");
INSERT INTO role_has_permissions VALUES("34","6");
INSERT INTO role_has_permissions VALUES("35","6");
INSERT INTO role_has_permissions VALUES("36","6");
INSERT INTO role_has_permissions VALUES("37","6");
INSERT INTO role_has_permissions VALUES("38","6");
INSERT INTO role_has_permissions VALUES("39","6");
INSERT INTO role_has_permissions VALUES("40","6");
INSERT INTO role_has_permissions VALUES("41","6");
INSERT INTO role_has_permissions VALUES("42","6");
INSERT INTO role_has_permissions VALUES("43","6");
INSERT INTO role_has_permissions VALUES("44","6");
INSERT INTO role_has_permissions VALUES("45","6");
INSERT INTO role_has_permissions VALUES("46","6");
INSERT INTO role_has_permissions VALUES("47","6");
INSERT INTO role_has_permissions VALUES("48","6");
INSERT INTO role_has_permissions VALUES("49","6");
INSERT INTO role_has_permissions VALUES("50","6");
INSERT INTO role_has_permissions VALUES("51","6");
INSERT INTO role_has_permissions VALUES("52","6");
INSERT INTO role_has_permissions VALUES("53","6");
INSERT INTO role_has_permissions VALUES("54","6");
INSERT INTO role_has_permissions VALUES("55","6");
INSERT INTO role_has_permissions VALUES("56","6");
INSERT INTO role_has_permissions VALUES("57","6");
INSERT INTO role_has_permissions VALUES("58","6");
INSERT INTO role_has_permissions VALUES("61","6");
INSERT INTO role_has_permissions VALUES("62","6");
INSERT INTO role_has_permissions VALUES("63","6");
INSERT INTO role_has_permissions VALUES("64","6");
INSERT INTO role_has_permissions VALUES("65","6");
INSERT INTO role_has_permissions VALUES("66","6");
INSERT INTO role_has_permissions VALUES("67","6");
INSERT INTO role_has_permissions VALUES("68","6");
INSERT INTO role_has_permissions VALUES("69","6");
INSERT INTO role_has_permissions VALUES("70","6");
INSERT INTO role_has_permissions VALUES("71","6");
INSERT INTO role_has_permissions VALUES("72","6");
INSERT INTO role_has_permissions VALUES("73","6");
INSERT INTO role_has_permissions VALUES("74","6");
INSERT INTO role_has_permissions VALUES("75","6");
INSERT INTO role_has_permissions VALUES("76","6");
INSERT INTO role_has_permissions VALUES("77","6");
INSERT INTO role_has_permissions VALUES("78","6");
INSERT INTO role_has_permissions VALUES("79","6");
INSERT INTO role_has_permissions VALUES("82","6");
INSERT INTO role_has_permissions VALUES("84","6");
INSERT INTO role_has_permissions VALUES("85","6");
INSERT INTO role_has_permissions VALUES("86","6");
INSERT INTO role_has_permissions VALUES("87","6");
INSERT INTO role_has_permissions VALUES("88","6");
INSERT INTO role_has_permissions VALUES("89","6");
INSERT INTO role_has_permissions VALUES("90","6");
INSERT INTO role_has_permissions VALUES("91","6");
INSERT INTO role_has_permissions VALUES("92","6");
INSERT INTO role_has_permissions VALUES("93","6");
INSERT INTO role_has_permissions VALUES("94","6");
INSERT INTO role_has_permissions VALUES("95","6");
INSERT INTO role_has_permissions VALUES("96","6");
INSERT INTO role_has_permissions VALUES("97","6");
INSERT INTO role_has_permissions VALUES("98","6");
INSERT INTO role_has_permissions VALUES("99","6");
INSERT INTO role_has_permissions VALUES("101","6");
INSERT INTO role_has_permissions VALUES("102","6");
INSERT INTO role_has_permissions VALUES("103","6");
INSERT INTO role_has_permissions VALUES("105","6");
INSERT INTO role_has_permissions VALUES("106","6");
INSERT INTO role_has_permissions VALUES("107","6");
INSERT INTO role_has_permissions VALUES("108","6");
INSERT INTO role_has_permissions VALUES("109","6");
INSERT INTO role_has_permissions VALUES("110","6");
INSERT INTO role_has_permissions VALUES("111","6");
INSERT INTO role_has_permissions VALUES("112","6");
INSERT INTO role_has_permissions VALUES("113","6");
INSERT INTO role_has_permissions VALUES("114","6");
INSERT INTO role_has_permissions VALUES("115","6");
INSERT INTO role_has_permissions VALUES("116","6");
INSERT INTO role_has_permissions VALUES("117","6");
INSERT INTO role_has_permissions VALUES("118","6");
INSERT INTO role_has_permissions VALUES("119","6");
INSERT INTO role_has_permissions VALUES("120","6");
INSERT INTO role_has_permissions VALUES("121","6");
INSERT INTO role_has_permissions VALUES("122","6");
INSERT INTO role_has_permissions VALUES("123","6");
INSERT INTO role_has_permissions VALUES("124","6");
INSERT INTO role_has_permissions VALUES("125","6");
INSERT INTO role_has_permissions VALUES("126","6");



CREATE TABLE `roles` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `guard_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO roles VALUES("1","Admin","Admins have full access to the system","1","2018-06-02 02:01:44","2023-06-21 16:53:36","web");
INSERT INTO roles VALUES("2","Owner","Full access to the system except technical aspects.","1","2018-10-22 04:53:13","2023-09-17 02:22:38","web");
INSERT INTO roles VALUES("3","Cashier","Cashier has limited access to sales data","1","2018-06-02 02:20:27","2023-06-21 16:53:46","web");
INSERT INTO roles VALUES("5","Customer","Customers can access their own data","1","2020-11-05 08:58:16","2020-11-15 02:39:15","web");
INSERT INTO roles VALUES("6","Admin (Demo)","Demo admin with restricted access to system settings","1","2023-08-12 11:10:13","2023-08-12 11:10:13","web");



CREATE TABLE `sales` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `cash_register_id` int DEFAULT NULL,
  `table_id` int DEFAULT NULL,
  `queue` int DEFAULT NULL,
  `customer_id` int NOT NULL,
  `warehouse_id` int NOT NULL,
  `biller_id` int NOT NULL,
  `item` int NOT NULL,
  `total_qty` double NOT NULL,
  `total_discount` double NOT NULL,
  `total_tax` double NOT NULL,
  `total_price` double NOT NULL,
  `grand_total` double NOT NULL,
  `currency_id` int DEFAULT NULL,
  `exchange_rate` double DEFAULT NULL,
  `order_tax_rate` double DEFAULT NULL,
  `order_tax` double DEFAULT NULL,
  `order_discount_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order_discount_value` double DEFAULT NULL,
  `order_discount` double DEFAULT NULL,
  `coupon_id` int DEFAULT NULL,
  `coupon_discount` double DEFAULT NULL,
  `shipping_cost` double DEFAULT NULL,
  `sale_status` int NOT NULL,
  `payment_status` int NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `paid_amount` double DEFAULT NULL,
  `sale_note` text COLLATE utf8mb4_unicode_ci,
  `staff_note` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `sms_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_default_ecommerce` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `stock_counts` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `warehouse_id` int NOT NULL,
  `category_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `brand_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_id` int NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `initial_file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `final_file` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `is_adjusted` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `suppliers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `vat_number` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO suppliers VALUES("1","Default Supplier","","Supplier Co","","<EMAIL>","###","Somewhere","Geo","","","","1","2023-09-17 02:50:09","2023-09-17 02:50:09");



CREATE TABLE `tables` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `number_of_person` int DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `taxes` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `rate` double NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `transfers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `reference_no` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int NOT NULL,
  `status` int NOT NULL,
  `from_warehouse_id` int NOT NULL,
  `to_warehouse_id` int NOT NULL,
  `item` int NOT NULL,
  `total_qty` double NOT NULL,
  `total_tax` double NOT NULL,
  `total_cost` double NOT NULL,
  `shipping_cost` double DEFAULT NULL,
  `grand_total` double NOT NULL,
  `document` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `note` text COLLATE utf8mb4_unicode_ci,
  `is_sent` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `units` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `unit_code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `unit_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `base_unit` int DEFAULT NULL,
  `operator` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `operation_value` double DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO units VALUES("1","Pc(s)","Piece","","*","1","1","2023-09-17 02:59:06","2023-09-17 02:59:06");



CREATE TABLE `users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `role_id` int NOT NULL,
  `biller_id` int DEFAULT NULL,
  `warehouse_id` int DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL,
  `is_deleted` tinyint(1) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO users VALUES("1","admin","<EMAIL>","$2y$10$xLU5AdwC1CENsZL7J7QL/OwZm4VVU5rvbo27hUmERIYTTHQlQmBgG","","2023-08-12 08:06:23","2024-10-12 15:32:46","******-0000001","My Company","1","1","1","1","0");



CREATE TABLE `variants` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;




CREATE TABLE `warehouses` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO warehouses VALUES("1","Default Warehouse","000","","Local","1","2023-09-17 02:58:36","2023-09-17 02:58:36");

