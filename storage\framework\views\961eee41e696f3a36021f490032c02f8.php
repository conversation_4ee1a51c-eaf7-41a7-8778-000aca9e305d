 <?php $__env->startSection('content'); ?>

<?php if(session()->has('message')): ?>
  <div class="alert alert-success alert-dismissible text-center"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button><?php echo e(session()->get('message')); ?></div>
<?php endif; ?>
<?php if(session()->has('not_permitted')): ?>
  <div class="alert alert-danger alert-dismissible text-center"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button><?php echo e(session()->get('not_permitted')); ?></div>
<?php endif; ?>
<section class="forms">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <h4><?php echo e(trans('file.General Setting')); ?></h4>
                    </div>
                    <div class="card-body">
                        <p class="italic"><small><?php echo e(trans('file.The field labels marked with * are required input fields')); ?>.</small></p>
                        <?php echo Form::open(['route' => 'setting.generalStore', 'files' => true, 'method' => 'post']); ?>

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.System Title')); ?> *</label>
                                        <input type="text" name="site_title" class="form-control" value="<?php if($lims_general_setting_data): ?><?php echo e($lims_general_setting_data->site_title); ?><?php endif; ?>" required />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.System Logo')); ?> *</label>
                                        <input type="file" name="site_logo" class="form-control" value=""/>
                                    </div>
                                    <?php if($errors->has('site_logo')): ?>
                                   <span>
                                       <strong><?php echo e($errors->first('site_logo')); ?></strong>
                                    </span>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-4 mt-4">
                                    <div class="form-group">
                                        <?php if($lims_general_setting_data->is_rtl): ?>
                                        <input type="checkbox" name="is_rtl" value="1" checked>
                                        <?php else: ?>
                                        <input type="checkbox" name="is_rtl" value="1" />
                                        <?php endif; ?>
                                        &nbsp;
                                        <label><?php echo e(trans('file.RTL Layout')); ?></label>

                                    </div>
                                </div>
                                <?php if(config('database.connections.saleprosaas_landlord')): ?>
                                    <div class="col-md-4 mt-4">
                                        <div class="form-group">
                                            <?php if($lims_general_setting_data->is_zatca): ?>
                                            <input type="checkbox" name="is_zatca" value="1" checked>
                                            <?php else: ?>
                                            <input type="checkbox" name="is_zatca" value="1" />
                                            <?php endif; ?>
                                            &nbsp;
                                            <label><?php echo e(trans('file.ZATCA QrCode')); ?></label>

                                        </div>
                                    </div>
                                <?php endif; ?>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Company Name')); ?></label>
                                        <input type="text" name="company_name" class="form-control" value="<?php if($lims_general_setting_data): ?><?php echo e($lims_general_setting_data->company_name); ?><?php endif; ?>" />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.VAT Registration Number')); ?></label>
                                        <input type="text" name="vat_registration_number" class="form-control" value="<?php if($lims_general_setting_data): ?><?php echo e($lims_general_setting_data->vat_registration_number); ?><?php endif; ?>" />
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Time Zone')); ?></label>
                                        <?php if($lims_general_setting_data): ?>
                                        <input type="hidden" name="timezone_hidden" value="<?php echo e(env('APP_TIMEZONE')); ?>">
                                        <?php endif; ?>
                                        <select name="timezone" class="selectpicker form-control" data-live-search="true" title="Select TimeZone...">
                                            <?php $__currentLoopData = $zones_array; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $zone): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($zone['zone']); ?>"><?php echo e($zone['diff_from_GMT'] . ' - ' . $zone['zone']); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Sale and Quotation without stock')); ?> *</label><br>
                                        <?php if($lims_general_setting_data->without_stock == 'yes'): ?>
                                        <label class="radio-inline">
                                            <input type="radio" name="without_stock" value="yes" checked> <?php echo e(trans('file.Yes')); ?>

                                        </label>
                                        <label class="radio-inline">
                                          <input type="radio" name="without_stock" value="no"> <?php echo e(trans('file.No')); ?>

                                        </label>
                                        <?php else: ?>
                                        <label class="radio-inline">
                                            <input type="radio" name="without_stock" value="yes"> <?php echo e(trans('file.Yes')); ?>

                                        </label>
                                        <label class="radio-inline">
                                          <input type="radio" name="without_stock" value="no" checked> <?php echo e(trans('file.No')); ?>

                                        </label>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Packing Slip to manage orders/sales')); ?> *</label><br>
                                        <?php if($lims_general_setting_data->is_packing_slip): ?>
                                        <label class="radio-inline">
                                            <input type="radio" name="is_packing_slip" value="1" checked> <?php echo e(trans('file.Enable')); ?>

                                        </label>
                                        <label class="radio-inline">
                                          <input type="radio" name="is_packing_slip" value="0"> <?php echo e(trans('file.Disable')); ?>

                                        </label>
                                        <?php else: ?>
                                        <label class="radio-inline">
                                            <input type="radio" name="is_packing_slip" value="1"> <?php echo e(trans('file.Enable')); ?>

                                        </label>
                                        <label class="radio-inline">
                                          <input type="radio" name="is_packing_slip" value="0" checked> <?php echo e(trans('file.Disable')); ?>

                                        </label>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Currency')); ?> *</label>
                                        <select name="currency" class="form-control" required>
                                            <?php $__currentLoopData = $lims_currency_list; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($lims_general_setting_data->currency == $currency->id): ?>
                                                    <option value="<?php echo e($currency->id); ?>" selected><?php echo e($currency->name); ?></option>
                                                <?php else: ?>
                                                    <option value="<?php echo e($currency->id); ?>"><?php echo e($currency->name); ?></option>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Currency Position')); ?> *</label><br>
                                        <?php if($lims_general_setting_data->currency_position == 'prefix'): ?>
                                        <label class="radio-inline">
                                            <input type="radio" name="currency_position" value="prefix" checked> <?php echo e(trans('file.Prefix')); ?>

                                        </label>
                                        <label class="radio-inline">
                                          <input type="radio" name="currency_position" value="suffix"> <?php echo e(trans('file.Suffix')); ?>

                                        </label>
                                        <?php else: ?>
                                        <label class="radio-inline">
                                            <input type="radio" name="currency_position" value="prefix"> <?php echo e(trans('file.Prefix')); ?>

                                        </label>
                                        <label class="radio-inline">
                                          <input type="radio" name="currency_position" value="suffix" checked> <?php echo e(trans('file.Suffix')); ?>

                                        </label>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Digits after deciaml point')); ?>*</label>
                                        <input class="form-control" type="number" name="decimal" value="<?php if($lims_general_setting_data): ?><?php echo e($lims_general_setting_data->decimal); ?><?php endif; ?>" max="6" min="0">
                                    </div>
                                </div>
                                <div class="col-md-4 d-none">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Theme')); ?> *</label>
                                        <div class="row ml-1">
                                            <div class="col-md-3 theme-option" data-color="default.css" style="background: #7c5cc4; min-height: 40px; max-width: 50px;" title="Purple"></div>&nbsp;&nbsp;
                                            <div class="col-md-3 theme-option" data-color="green.css" style="background: #1abc9c; min-height: 40px;max-width: 50px;" title="Green"></div>&nbsp;&nbsp;
                                            <div class="col-md-3 theme-option" data-color="blue.css" style="background: #3498db; min-height: 40px;max-width: 50px;" title="Blue"></div>&nbsp;&nbsp;
                                            <div class="col-md-3 theme-option" data-color="dark.css" style="background: #34495e; min-height: 40px;max-width: 50px;" title="Dark"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Staff Access')); ?> *</label>
                                        <?php if($lims_general_setting_data): ?>
                                        <input type="hidden" name="staff_access_hidden" value="<?php echo e($lims_general_setting_data->staff_access); ?>">
                                        <?php endif; ?>
                                        <select name="staff_access" class="selectpicker form-control">
                                            <option value="all"> <?php echo e(trans('file.All Records')); ?></option>
                                            <option value="own"> <?php echo e(trans('file.Own Records')); ?></option>
                                            <option value="warehouse"> <?php echo e(trans('file.Warehouse Wise')); ?></option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Invoice Format')); ?> *</label>
                                        <?php if($lims_general_setting_data): ?>
                                        <input type="hidden" name="invoice_format_hidden" value="<?php echo e($lims_general_setting_data->invoice_format); ?>">
                                        <?php endif; ?>
                                        <select name="invoice_format" class="selectpicker form-control" required>
                                            <option value="standard">Standard</option>
                                            <option value="gst">Indian GST</option>
                                        </select>
                                    </div>
                                </div>
                                <div id="state" class="col-md-4 d-none">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.State')); ?> *</label>
                                        <?php if($lims_general_setting_data): ?>
                                        <input type="hidden" name="state_hidden" value="<?php echo e($lims_general_setting_data->state); ?>">
                                        <?php endif; ?>
                                        <select name="state" class="selectpicker form-control">
                                            <option value="1">Home State</option>
                                            <option value="2">Buyer State</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Date Format')); ?> *</label>
                                        <?php if($lims_general_setting_data): ?>
                                        <input type="hidden" name="date_format_hidden" value="<?php echo e($lims_general_setting_data->date_format); ?>">
                                        <?php endif; ?>
                                        <select name="date_format" class="selectpicker form-control">
                                            <option value="d-m-Y"> dd-mm-yyy</option>
                                            <option value="d/m/Y"> dd/mm/yyy</option>
                                            <option value="d.m.Y"> dd.mm.yyy</option>
                                            <option value="m-d-Y"> mm-dd-yyy</option>
                                            <option value="m/d/Y"> mm/dd/yyy</option>
                                            <option value="m.d.Y"> mm.dd.yyy</option>
                                            <option value="Y-m-d"> yyy-mm-dd</option>
                                            <option value="Y/m/d"> yyy/mm/dd</option>
                                            <option value="Y.m.d"> yyy.mm.dd</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Developed By')); ?></label>
                                        <input type="text" name="developed_by" class="form-control" value="<?php echo e($lims_general_setting_data->developed_by); ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Expiry Duration Type')); ?></label>
                                        <select name="expiry_type" class="form-control">
                                                <option value="days" <?php echo e($lims_general_setting_data->expiry_type == 'days' ? 'selected' : ''); ?>>Days</option>
                                                <option value="months" <?php echo e($lims_general_setting_data->expiry_type == 'months' ? 'selected' : ''); ?>>Months</option>
                                                <option value="years" <?php echo e($lims_general_setting_data->expiry_type == 'years' ? 'selected' : ''); ?>>Years</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label><?php echo e(trans('file.Expiry Value')); ?></label>
                                        <input type="number" class="form-control" name="expiry_value" value="<?php echo e($lims_general_setting_data->expiry_value ?? '0'); ?>">
                                    </div>
                                </div>
                                <?php if(config('database.connections.saleprosaas_landlord')): ?>
                                    <br>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Subscription Type')); ?></label>
                                            <p><?php echo e($lims_general_setting_data->subscription_type); ?></p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Package Name')); ?></label>
                                            <p id="package-name"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Monthly Fee')); ?></label>
                                            <p id="monthly-fee"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Yearly Fee')); ?></label>
                                            <p id="yearly-fee"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Number of Warehouses')); ?></label>
                                            <p id="number-of-warehouse"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Number of Products')); ?></label>
                                            <p id="number-of-product"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Number of Invoices')); ?></label>
                                            <p id="number-of-invoice"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Number of User Account')); ?></label>
                                            <p id="number-of-user-account"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Number of Employees')); ?></label>
                                            <p id="number-of-employee"></p>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label><?php echo e(trans('file.Subscription Ends at')); ?></label>
                                            <p><?php echo e(date($lims_general_setting_data->date_format, strtotime($lims_general_setting_data->expiry_date))); ?></p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="form-group">
                                <input type="submit" value="<?php echo e(trans('file.submit')); ?>" class="btn btn-primary">
                            </div>
                        <?php echo Form::close(); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script type="text/javascript">
    $("ul#setting").siblings('a').attr('aria-expanded','true');
    $("ul#setting").addClass("show");
    $("ul#setting #general-setting-menu").addClass("active");

    $("select[name=invoice_format]").on("change", function (argument) {
        if($(this).val() == 'standard') {
            $("#state").addClass('d-none');
            $("input[name=state]").prop("required", false);
        }
        else if($(this).val() == 'gst') {
            $("#state").removeClass('d-none');
            $("input[name=state]").prop("required", true);
        }
    })
    if($("input[name='timezone_hidden']").val()){
        $('select[name=timezone]').val($("input[name='timezone_hidden']").val());
        $('select[name=staff_access]').val($("input[name='staff_access_hidden']").val());
        $('select[name=date_format]').val($("input[name='date_format_hidden']").val());
        $('select[name=invoice_format]').val($("input[name='invoice_format_hidden']").val());
        if($("input[name='invoice_format_hidden']").val() == 'gst') {
            $('select[name=state]').val($("input[name='state_hidden']").val());
            $("#state").removeClass('d-none');
        }
        $('.selectpicker').selectpicker('refresh');
    }

    $('.theme-option').on('click', function() {
        $.get('general_setting/change-theme/' + $(this).data('color'), function(data) {
        });
        var style_link= $('#custom-style').attr('href').replace(/([^-]*)$/, $(this).data('color') );
        $('#custom-style').attr('href', style_link);
    });

    <?php if(config('database.connections.saleprosaas_landlord')): ?>
        $.ajax({
            type: 'GET',
            async: false,
            url: '<?php echo e(route("package.fetchData", $lims_general_setting_data->package_id)); ?>',
            success: function(data) {
                $("#package-name").text(data['name']);
                $("#monthly-fee").text(data['monthly_fee']);
                $("#yearly-fee").text(data['yearly_fee']);
                $("#package-name").text(data['name']);

                if(data['number_of_warehouse'])
                    $("#number-of-warehouse").text(data['number_of_warehouse']);
                else
                    $("#number-of-warehouse").text('Unlimited');

                if(data['number_of_product'])
                    $("#number-of-product").text(data['number_of_product']);
                else
                    $("#number-of-product").text('Unlimited');

                if(data['number_of_invoice'])
                    $("#number-of-invoice").text(data['number_of_invoice']);
                else
                    $("#number-of-invoice").text('Unlimited');

                if(data['number_of_user_account'])
                    $("#number-of-user-account").text(data['number_of_user_account']);
                else
                    $("#number-of-user-account").text('Unlimited');

                if(data['number_of_employee'])
                    $("#number-of-employee").text(data['number_of_employee']);
                else
                    $("#number-of-employee").text('Unlimited');
            }
        });
    <?php endif; ?>
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layout.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\sale\resources\views/backend/setting/general_setting.blade.php ENDPATH**/ ?>