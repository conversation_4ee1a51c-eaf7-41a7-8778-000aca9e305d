<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class CafeSampleDataSeeder extends Seeder
{
    /**
     * Run the cafe sample data seeders.
     *
     * @return void
     */
    public function run()
    {
        echo "🚀 Starting Cafe Sample Data Creation...\n\n";
        
        // Run all cafe sample data seeders in sequence
        $this->call([
            CafeTeaSampleDataSeeder::class,
            CafeTeaSampleDataSeeder2::class,
            CafeTeaSampleDataSeeder3::class,
        ]);
        
        echo "\n🎉 Cafe Sample Data Creation Complete!\n";
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
        echo "Your cafe management system is now ready with:\n\n";
        echo "📋 CATEGORIES:\n";
        echo "   • Hot Beverages (Tea, Coffee)\n";
        echo "   • Cold Beverages (Iced Drinks, Smoothies)\n";
        echo "   • Food Items (Pastries, Sandwiches, Desserts, Snacks)\n";
        echo "   • Raw Materials (Coffee beans, Tea leaves, Milk, etc.)\n\n";
        
        echo "🍵 SAMPLE PRODUCTS:\n";
        echo "   • Teas: Earl Grey, Green Tea, Chamomile\n";
        echo "   • Coffees: E<PERSON>resso, <PERSON>puccino, Latte, Americano\n";
        echo "   • Cold Drinks: Iced Coffee, Iced Tea, Frappuccino\n";
        echo "   • Smoothies: Mango, Berry Blast\n";
        echo "   • Pastries: Croissant, Muffins, Danish\n";
        echo "   • Sandwiches: Club, Grilled Cheese, Turkey\n";
        echo "   • Desserts: Chocolate Cake, Cheesecake, Tiramisu\n";
        echo "   • Snacks: Chips, Nuts, Cookies\n";
        echo "   • Raw Materials: Coffee beans, Tea leaves, Milk, Sugar\n\n";
        
        echo "👥 CUSTOMER GROUPS:\n";
        echo "   • Regular Customer (0% discount)\n";
        echo "   • VIP Customer (10% discount)\n";
        echo "   • Staff Discount (20% discount)\n";
        echo "   • Student Discount (5% discount)\n\n";
        
        echo "🏪 SUPPLIERS:\n";
        echo "   • Premium Coffee Co. (Coffee beans)\n";
        echo "   • Global Tea Ltd. (Tea leaves)\n";
        echo "   • Fresh Bakes Inc. (Pastries & food)\n";
        echo "   • Farm Fresh Dairy (Milk & dairy products)\n\n";
        
        echo "🪑 RESTAURANT TABLES:\n";
        echo "   • Table 1 (2 persons) - Window table\n";
        echo "   • Table 2 (4 persons) - Center table\n";
        echo "   • Table 3 (6 persons) - Large table\n";
        echo "   • Counter (1 person) - Counter seating\n";
        echo "   • Outdoor 1 (4 persons) - Patio table\n\n";
        
        echo "💰 PRICING EXAMPLES:\n";
        echo "   • Earl Grey Tea: $3.50\n";
        echo "   • Cappuccino: $4.50\n";
        echo "   • Latte: $5.00\n";
        echo "   • Mango Smoothie: $6.00\n";
        echo "   • Club Sandwich: $8.50\n";
        echo "   • Chocolate Cake: $6.50\n\n";
        
        echo "🎯 NEXT STEPS:\n";
        echo "   1. Go to Products → Product List to see all items\n";
        echo "   2. Use POS system to start making sales\n";
        echo "   3. Add more customers as needed\n";
        echo "   4. Create purchases from suppliers\n";
        echo "   5. Generate reports to track performance\n\n";
        
        echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n";
        echo "🎉 Your Cafe Management System is Ready to Use! ☕\n";
    }
}
