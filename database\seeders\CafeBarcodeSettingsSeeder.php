<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Barcode;

class CafeBarcodeSettingsSeeder extends Seeder
{
    public function run()
    {
        // Clear existing custom barcodes
        Barcode::where('is_custom', true)->delete();

        // Create common barcode settings for cafe business
        $barcodeSettings = [
            [
                'name' => 'Cafe Standard Labels',
                'description' => 'Standard 2x1 inch labels for cafe products - 30 labels per sheet (Letter size)',
                'width' => 2.0000,
                'height' => 1.0000,
                'paper_width' => 8.5000,
                'paper_height' => 11.0000,
                'top_margin' => 0.5000,
                'left_margin' => 0.2500,
                'row_distance' => 0.0000,
                'col_distance' => 0.0000,
                'stickers_in_one_row' => 3,
                'stickers_in_one_sheet' => 30,
                'is_default' => 1,
                'is_continuous' => 0,
                'is_custom' => 1,
            ],
            [
                'name' => 'Small Product Labels',
                'description' => 'Small 1.5x0.75 inch labels for small items like tea bags, sugar packets - 40 labels per sheet',
                'width' => 1.5000,
                'height' => 0.7500,
                'paper_width' => 8.5000,
                'paper_height' => 11.0000,
                'top_margin' => 0.5000,
                'left_margin' => 0.5000,
                'row_distance' => 0.1250,
                'col_distance' => 0.1250,
                'stickers_in_one_row' => 4,
                'stickers_in_one_sheet' => 40,
                'is_default' => 0,
                'is_continuous' => 0,
                'is_custom' => 1,
            ],
            [
                'name' => 'Large Menu Labels',
                'description' => 'Large 3x2 inch labels for menu boards and large containers - 8 labels per sheet',
                'width' => 3.0000,
                'height' => 2.0000,
                'paper_width' => 8.5000,
                'paper_height' => 11.0000,
                'top_margin' => 0.5000,
                'left_margin' => 1.2500,
                'row_distance' => 0.2500,
                'col_distance' => 0.2500,
                'stickers_in_one_row' => 2,
                'stickers_in_one_sheet' => 8,
                'is_default' => 0,
                'is_continuous' => 0,
                'is_custom' => 1,
            ],
            [
                'name' => 'Price Tag Labels',
                'description' => 'Square 1x1 inch price tags for display cases and shelves - 48 labels per sheet',
                'width' => 1.0000,
                'height' => 1.0000,
                'paper_width' => 8.5000,
                'paper_height' => 11.0000,
                'top_margin' => 0.5000,
                'left_margin' => 0.7500,
                'row_distance' => 0.0000,
                'col_distance' => 0.0000,
                'stickers_in_one_row' => 6,
                'stickers_in_one_sheet' => 48,
                'is_default' => 0,
                'is_continuous' => 0,
                'is_custom' => 1,
            ],
            [
                'name' => 'Bottle/Cup Labels',
                'description' => 'Narrow 2.5x1 inch labels perfect for bottles, cups, and cylindrical containers - 20 labels per sheet',
                'width' => 2.5000,
                'height' => 1.0000,
                'paper_width' => 8.5000,
                'paper_height' => 11.0000,
                'top_margin' => 0.5000,
                'left_margin' => 0.7500,
                'row_distance' => 0.1250,
                'col_distance' => 0.1250,
                'stickers_in_one_row' => 3,
                'stickers_in_one_sheet' => 20,
                'is_default' => 0,
                'is_continuous' => 0,
                'is_custom' => 1,
            ],
            [
                'name' => 'Inventory Labels',
                'description' => 'Medium 2x1.5 inch labels for inventory management and storage containers - 15 labels per sheet',
                'width' => 2.0000,
                'height' => 1.5000,
                'paper_width' => 8.5000,
                'paper_height' => 11.0000,
                'top_margin' => 0.5000,
                'left_margin' => 1.2500,
                'row_distance' => 0.1250,
                'col_distance' => 0.1250,
                'stickers_in_one_row' => 3,
                'stickers_in_one_sheet' => 15,
                'is_default' => 0,
                'is_continuous' => 0,
                'is_custom' => 1,
            ],
            [
                'name' => 'Thermal Roll Labels',
                'description' => 'Continuous thermal roll labels 2x1 inch for thermal printers - 28 labels per roll',
                'width' => 2.0000,
                'height' => 1.0000,
                'paper_width' => 2.2500,
                'paper_height' => 0.0000,
                'top_margin' => 0.0000,
                'left_margin' => 0.1250,
                'row_distance' => 0.0000,
                'col_distance' => 0.0000,
                'stickers_in_one_row' => 1,
                'stickers_in_one_sheet' => 28,
                'is_default' => 0,
                'is_continuous' => 1,
                'is_custom' => 1,
            ],
            [
                'name' => 'Round Labels',
                'description' => 'Round 1.5 inch diameter labels for jars and round containers - 24 labels per sheet',
                'width' => 1.5000,
                'height' => 1.5000,
                'paper_width' => 8.5000,
                'paper_height' => 11.0000,
                'top_margin' => 0.5000,
                'left_margin' => 0.5000,
                'row_distance' => 0.2500,
                'col_distance' => 0.2500,
                'stickers_in_one_row' => 4,
                'stickers_in_one_sheet' => 24,
                'is_default' => 0,
                'is_continuous' => 0,
                'is_custom' => 1,
            ],
        ];

        foreach ($barcodeSettings as $setting) {
            Barcode::create($setting);
        }

        echo "✅ Cafe barcode settings created successfully!\n";
        echo "📋 Created 8 different barcode label formats:\n";
        echo "   • Cafe Standard Labels (2x1 inch) - DEFAULT\n";
        echo "   • Small Product Labels (1.5x0.75 inch)\n";
        echo "   • Large Menu Labels (3x2 inch)\n";
        echo "   • Price Tag Labels (1x1 inch)\n";
        echo "   • Bottle/Cup Labels (2.5x1 inch)\n";
        echo "   • Inventory Labels (2x1.5 inch)\n";
        echo "   • Thermal Roll Labels (2x1 inch)\n";
        echo "   • Round Labels (1.5 inch diameter)\n\n";
        echo "🎯 Perfect for cafe product labeling and inventory management!\n";
    }
}
