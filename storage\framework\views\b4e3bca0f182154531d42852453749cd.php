 <?php $__env->startSection('content'); ?>

<?php if(session()->has('message')): ?>
<div class="alert alert-success alert-dismissible text-center"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button><?php echo e(session()->get('message')); ?></div>
<?php endif; ?>
<?php if(session()->has('not_permitted')): ?>
<div class="alert alert-danger alert-dismissible text-center"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button><?php echo e(session()->get('not_permitted')); ?></div>
<?php endif; ?>

<?php $__env->startPush('css'); ?>
<style>
    .custom-switch {
        padding-left: .5rem;
    }

    .custom-switch .custom-control-label::before {
        left: -2.25rem;
        width: 1.75rem;
        pointer-events: all;
        border-radius: .5rem;
    }

    .custom-switch .custom-control-label::after {
        top: calc(.25rem + 2px);
        left: calc(-2.25rem + 2px);
        width: calc(1rem - 4px);
        height: calc(1rem - 4px);
        background-color: #adb5bd;
        border-radius: .5rem;
        transition: background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-transform .15s ease-in-out;
        transition: transform .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out;
        transition: transform .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out, -webkit-transform .15s ease-in-out;
    }

    .custom-control-input:checked~.custom-control-label::before {
        color: #fff;
        border-color: #007bff;
        background-color: #007bff;
    }

    .custom-switch .custom-control-input:checked~.custom-control-label::after {
        background-color: #fff;
        -webkit-transform: translateX(.75rem);
        transform: translateX(.75rem);
    }
</style>
<?php $__env->stopPush(); ?>
<section class="forms">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <h4><?php echo e(trans('file.Payment Gateways')); ?></h4>
                    </div>
                    <div class="card-body">
                        <p class="italic"><small><?php echo e(trans('file.The field labels marked with * are required input fields')); ?>.</small></p>
                        <?php echo Form::open(['route' => 'setting.gateway.update', 'files' => true, 'method' => 'post']); ?>

                        <div class="row">
                            <?php $__currentLoopData = $payment_gateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pg): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-12 mt-3 mb-3">
                                <h4 class="d-flex align-items-center justify-content-between">
                                    <?php echo e($pg->name); ?> <?php echo e(trans('file.Details')); ?>


                                    <div style="width:200px">
                                        <select class="form-control" name="module_status[<?php echo e($loop->index); ?>][]" multiple>
                                            <?php
                                                $modules = json_decode($pg->module_status, true);
                                            ?>

                                            <?php $__currentLoopData = ['salepro', 'ecommerce']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($module); ?>" <?php echo e(!empty($modules[$module]) && $modules[$module] ? 'selected' : ''); ?>>
                                                    <?php echo e(ucfirst($module)); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </h4>
                                <hr>
                                <input type="hidden" name="pg_name[]" class="form-control" value="<?php echo e($pg->name); ?>" />
                                <?php
                                $lines = explode(';',$pg->details);
                                $keys = explode(',', $lines[0]);
                                $vals = explode(',', $lines[1]);

                                $results = array_combine($keys, $vals);
                                ?>
                                <?php $__currentLoopData = $results; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="form-group">
                                    <label><?php echo e($key); ?></label>
                                    <?php if($key == 'Mode'): ?>
                                        <select name="<?php echo e($pg->name.'_'.str_replace(' ','_',$key)); ?>" class="selectpicker form-control">
                                            <option <?php if($value == 'sandbox'): ?> selected <?php endif; ?> value="sandbox">Sandbox</option>
                                            <option <?php if($value == 'live'): ?> selected <?php endif; ?> value="live">Live</option>
                                        </select>
                                    <?php else: ?>
                                        <input type="text" name="<?php echo e($pg->name.'_'.str_replace(' ','_',$key)); ?>" class="form-control" value="<?php echo e($value); ?>" />
                                    <?php endif; ?>

                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        </div>
                        <div class="form-group">
                            <input type="submit" value="<?php echo e(trans('file.submit')); ?>" class="btn btn-primary">
                        </div>
                        <?php echo Form::close(); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    if ($('.activate').is(':checked')) {
        $(this).siblings('input[type="text"]').val(1);
    } else {
        $(this).siblings('input[type="text"]').val(0);
    }
    $(document).on('click', '.activate', function(){
        if ($(this).is(':checked')) {
            $(this).siblings('input[type="hidden"]').val(1);
        } else if (!$(this).is(':checked')) {
            $(this).siblings('input[type="hidden"]').val(0);
        }
    })
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('backend.layout.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\sale\resources\views/backend/setting/payment-gateways.blade.php ENDPATH**/ ?>