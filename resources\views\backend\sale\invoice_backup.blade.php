<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" type="image/png" href="{{url('logo', $general_setting->site_logo)}}" />
    <title>{{$general_setting->site_title}}</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="robots" content="all,follow">

    <style type="text/css">
        * {
            font-size: 14px;
            line-height: 24px;
            font-family: 'Ubuntu', sans-serif;
            text-transform: capitalize;
        }
        .btn {
            padding: 7px 10px;
            text-decoration: none;
            border: none;
            display: block;
            text-align: center;
            margin: 7px;
            cursor:pointer;
        }

        .btn-info {
            background-color: #999;
            color: #FFF;
        }

        .btn-primary {
            background-color: #6449e7;
            color: #FFF;
            width: 100%;
        }
        td,
        th,
        tr,
        table {
            border-collapse: collapse;
        }
        tr {border-bottom: 1px dotted #ddd;}
        td,th {padding: 7px 0;}

        table {width: 100%;}
        tfoot tr th:first-child {text-align: left;}

        .centered {
            text-align: center;
            align-content: center;
        }
        small{font-size:11px;}

        @media print {
            * {
                font-size:12px;
                line-height: 20px;
            }
            td,th {padding: 5px 0;}
            .hidden-print {
                display: none !important;
            }
            @page { margin: 1.5cm 0.5cm 0.5cm; }
            @page:first { margin-top: 0.5cm; }
            /*tbody::after {
                content: ''; display: block;
                page-break-after: always;
                page-break-inside: avoid;
                page-break-before: avoid;
            }*/
        }
    </style>
  </head>
<body>

<div style="max-width:400px;margin:0 auto">
    @if(preg_match('~[0-9]~', url()->previous()))
        @php $url = '../../pos'; @endphp
    @else
        @php $url = url()->previous(); @endphp
    @endif
    <div class="hidden-print">
        <table>
            <tr>
                <td><a href="{{$url}}" class="btn btn-info"><i class="fa fa-arrow-left"></i> {{trans('file.Back')}}</a> </td>
                <td><button onclick="window.print();" class="btn btn-primary"><i class="dripicons-print"></i> {{trans('file.Print')}}</button></td>
            </tr>
        </table>
        <br>
    </div>

    <div id="receipt-data">
        <div class="centered">
            @if($general_setting->site_logo)
                <img src="{{url('logo', $general_setting->site_logo)}}" height="42" width="50" style="margin:10px 0;">
            @endif

            <h2>{{$lims_biller_data->company_name}}</h2>

            <p>{{trans('file.Address')}}: {{$lims_warehouse_data->address}}
                <br>{{trans('file.Phone Number')}}: {{$lims_warehouse_data->phone}}
                @if($lims_user_data)
                <br>{{trans('file.User')}}: {{$lims_user_data->name}}
                @endif
                @if($general_setting->vat_registration_number)
                <br>{{trans('file.VAT Number')}}: {{$general_setting->vat_registration_number}}
                @endif
            </p>
        </div>
        <div style="float:left;">
            <p>{{trans('file.Date')}}: {{date($general_setting->date_format, strtotime($lims_sale_data->created_at->toDateString()))}}<br>
                {{trans('file.reference')}}: {{$lims_sale_data->reference_no}}<br>
                {{trans('file.customer')}}: {{$lims_customer_data->name}}
                @if($lims_sale_data->table_id)
                <br>{{trans('file.Table')}}: {{$lims_sale_data->table->name}}
                <br>{{trans('file.Queue')}}: {{$lims_sale_data->queue}}
                @endif
                <?php
                    foreach($sale_custom_fields as $key => $fieldName) {
                        $field_name = str_replace(" ", "_", strtolower($fieldName));
                        echo '<br>'.$fieldName.': ' . $lims_sale_data->$field_name;
                    }
                    foreach($customer_custom_fields as $key => $fieldName) {
                        $field_name = str_replace(" ", "_", strtolower($fieldName));
                        echo '<br>'.$fieldName.': ' . $lims_customer_data->$field_name;
                    }
                ?>

            </p>
        </div>
        <div style="float:right;">
            <p><strong>Sales Invoice</strong></p>
        </div>
            
        <table class="table-data">
            <thead>
                <th>Description</th>
                <th>MRP</th>
                <th>Rate</th>
                <th>Qty</th>
                <th>Amount</th>
            </thead>
            <tbody>
                <?php 
                    $total_product_tax = 0;
                    $total_mrp = 0;
                ?>
                @foreach($lims_product_sale_data as $key => $product_sale_data)
                <?php
                    $lims_product_data = \App\Models\Product::find($product_sale_data->product_id);
                    if($product_sale_data->variant_id) {
                        $variant_data = \App\Models\Variant::find($product_sale_data->variant_id);
                        $product_name = $lims_product_data->name.' ['.$variant_data->name.']';
                    }
                    elseif($product_sale_data->product_batch_id) {
                        $product_batch_data = \App\Models\ProductBatch::select('batch_no')->find($product_sale_data->product_batch_id);
                        $product_name = $lims_product_data->name.' ['.trans("file.Batch No").':'.$product_batch_data->batch_no.']';
                    }
                    else
                        $product_name = $lims_product_data->name;

                    if($product_sale_data->imei_number) {
                        $product_name .= '<br>'.trans('IMEI or Serial Numbers').': '.$product_sale_data->imei_number;
                    }
                    $total_mrp += $lims_product_data->price * $product_sale_data->qty;
                ?>
                <tr>
                    <td>
                        {!!$product_name!!}
                        @foreach($product_custom_fields as $index => $fieldName)
                            <?php $field_name = str_replace(" ", "_", strtolower($fieldName)) ?>
                            @if($lims_product_data->$field_name)
                                @if(!$index)
                                <br>{{$fieldName.': '.$lims_product_data->$field_name}}
                                @else
                                {{'/'.$fieldName.': '.$lims_product_data->$field_name}}
                                @endif
                            @endif
                        @endforeach
                        <!-- <br>
                        @if($product_sale_data->tax_rate)
                            <?php $total_product_tax += $product_sale_data->tax ?>
                            [{{trans('file.Tax')}} ({{$product_sale_data->tax_rate}}%): {{$product_sale_data->tax}}]
                        @endif -->
                    </td>
                    <td style="text-align:center;">
                        {{$lims_product_data->price}}
                    </td>
                    <td style="text-align:center;">
                        {{$product_sale_data->total / $product_sale_data->qty}}
                    </td>
                    <td style="text-align:center;">
                        {{$product_sale_data->qty}}
                    </td>
                    <td style="text-align:right;">{{$product_sale_data->total}}</td>
                </tr>
                @endforeach

            <!-- <tfoot> -->
                <tr>
                    <th colspan="3" style="text-align:left">{{trans('file.Total')}}</th>
                    <th style="text-align:center;">{{$lims_sale_data->total_qty}}</th>
                    <th style="text-align:right">{{number_format((float)($lims_sale_data->total_price), $general_setting->decimal)}}</th>
                </tr>
                @if($general_setting->invoice_format == 'gst' && $general_setting->state == 1)
                <tr>
                    <td colspan="2">IGST</td>
                    <td style="text-align:right">{{number_format((float)($total_product_tax), $general_setting->decimal, '.', ',')}}</td>
                </tr>
                @elseif($general_setting->invoice_format == 'gst' && $general_setting->state == 2)
                <tr>
                    <td colspan="2">SGST</td>
                    <td style="text-align:right">{{number_format((float)($total_product_tax / 2), $general_setting->decimal, '.', ',')}}</td>
                </tr>
                <tr>
                    <td colspan="2">CGST</td>
                    <td style="text-align:right">{{number_format((float)($total_product_tax / 2), $general_setting->decimal, '.', ',')}}</td>
                </tr>
                @endif
                @if($lims_sale_data->order_tax)
                <tr>
                    <th colspan="4" style="text-align:left">{{trans('file.Order Tax')}}</th>
                    <th style="text-align:right">{{number_format((float)($lims_sale_data->order_tax), $general_setting->decimal, '.', ',')}}</th>
                </tr>
                @endif
                @if($lims_sale_data->total_discount || $lims_sale_data->order_discount)
                <tr>
                    <th colspan="4" style="text-align:left">{{trans('file.Discount')}}</th>
                    <th style="text-align:right">{{$lims_sale_data->total_discount + $lims_sale_data->order_discount}}</th>
                </tr>
                @endif
                @if($lims_sale_data->coupon_discount)
                <tr>
                    <th colspan="4" style="text-align:left">{{trans('file.Coupon Discount')}}</th>
                    <th style="text-align:right">{{number_format((float)($lims_sale_data->coupon_discount), $general_setting->decimal, '.', ',')}}</th>
                </tr>
                @endif
                @if($lims_sale_data->shipping_cost)
                <tr>
                    <th colspan="4" style="text-align:left">{{trans('file.Shipping Cost')}}</th>
                    <th style="text-align:right">{{number_format((float)($lims_sale_data->shipping_cost), $general_setting->decimal, '.', ',')}}</th>
                </tr>
                @endif
                <tr>
                    <th colspan="4" style="text-align:left">{{trans('file.grand total')}}</th>
                    <th style="text-align:right">{{number_format((float)($lims_sale_data->grand_total), $general_setting->decimal, '.', ',')}}</th>
                </tr>
                <tr>
                    @if($general_setting->currency_position == 'prefix')
                    <th class="centered" colspan="5">{{trans('file.In Words')}}: <span>{{$currency_code}}</span> <span>{{str_replace("-"," ",$numberInWords)}}</span></th>
                    @else
                    <th class="centered" colspan="5">{{trans('file.In Words')}}: <span>{{str_replace("-"," ",$numberInWords)}}</span> <span>{{$currency_code}}</span></th>
                    @endif
                </tr>
            </tbody>
            <!-- </tfoot> -->
        </table>
        @if($total_mrp - $lims_sale_data->total_price - $lims_sale_data->total_discount - $lims_sale_data->order_discount)
            <h3 style="background-color:#ddd; text-align: center;">SAVE: {{$total_mrp - $lims_sale_data->total_price - $lims_sale_data->total_discount - $lims_sale_data->order_discount}} BDT</h3>
        @endif
        <table>
            <tbody>
                @foreach($lims_payment_data as $payment_data)
                <tr style="background-color:#ddd;">
                    <td style="padding: 5px;width:30%">{{trans('file.Paid By')}}: {{$payment_data->paying_method}}</td>
                    <td style="padding: 5px;width:40%">{{trans('file.Amount')}}: {{number_format((float)($payment_data->amount), $general_setting->decimal, '.', ',')}}</td>
                    <td style="padding: 5px;width:30%">{{trans('file.Change')}}: {{number_format((float)$payment_data->change, $general_setting->decimal, '.', ',')}}</td>
                </tr>
                @endforeach
                <tr><td class="centered" colspan="3">Your patronage is greately appreciated. We eagerly await your next visit!</td></tr>
                <tr>
                    <td class="centered" colspan="3">
                    <?php echo '<img style="margin-top:10px;" src="data:image/png;base64,' . DNS1D::getBarcodePNG($lims_sale_data->reference_no, 'C128') . '" width="300" alt="barcode"   />';?>
                    <br>
                    <?php echo '<img style="margin-top:10px;" src="data:image/png;base64,' . DNS2D::getBarcodePNG($qrText, 'QRCODE') . '" alt="QRcode"   />';?>
                    </td>
                </tr>
            </tbody>
        </table>
        <!-- <div class="centered" style="margin:30px 0 50px">
            <small>{{trans('file.Invoice Generated By')}} {{$general_setting->site_title}}.
            {{trans('file.Developed By')}} LionCoders</strong></small>
        </div> -->
    </div>
</div>

<script type="text/javascript">
    localStorage.clear();
    function auto_print() {
        window.print();
    }
    //setTimeout(auto_print, 1000);
</script>

</body>
</html>
