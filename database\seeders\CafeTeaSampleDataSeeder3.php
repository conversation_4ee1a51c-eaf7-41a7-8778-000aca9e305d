<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\Tax;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\Product_Warehouse;

class CafeTeaSampleDataSeeder3 extends Seeder
{
    public function run()
    {
        $tax = Tax::where('name', 'VAT')->first();
        $warehouse = Warehouse::where('name', 'Main Warehouse')->first();

        // Final batch of products - Desserts, Snacks, Raw Materials
        $products = [
            // Desserts
            [
                'name' => 'Chocolate Cake Slice',
                'code' => 'DES001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 9, // Desserts
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 2.50,
                'price' => 6.50,
                'qty' => 15,
                'alert_quantity' => 3,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Rich chocolate cake slice',
            ],
            [
                'name' => 'Cheesecake Slice',
                'code' => 'DES002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 9, // Desserts
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 3.00,
                'price' => 7.50,
                'qty' => 12,
                'alert_quantity' => 2,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Creamy New York style cheesecake',
            ],
            [
                'name' => 'Tiramisu',
                'code' => 'DES003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 9, // Desserts
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 3.50,
                'price' => 8.50,
                'qty' => 10,
                'alert_quantity' => 2,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Classic Italian tiramisu',
            ],
            [
                'name' => 'Ice Cream Scoop',
                'code' => 'DES004',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 9, // Desserts
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 1.00,
                'price' => 3.00,
                'qty' => 50,
                'alert_quantity' => 10,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Single scoop of ice cream',
            ],

            // Snacks
            [
                'name' => 'Potato Chips',
                'code' => 'SNK001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 10, // Snacks
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 0.75,
                'price' => 2.50,
                'qty' => 40,
                'alert_quantity' => 10,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Crispy potato chips',
            ],
            [
                'name' => 'Mixed Nuts',
                'code' => 'SNK002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 10, // Snacks
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 1.50,
                'price' => 4.00,
                'qty' => 30,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Premium mixed nuts',
            ],
            [
                'name' => 'Cookies (Pack)',
                'code' => 'SNK003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 10, // Snacks
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 1.25,
                'price' => 3.50,
                'qty' => 25,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Pack of assorted cookies',
            ],

            // Raw Materials
            [
                'name' => 'Coffee Beans (Arabica)',
                'code' => 'RAW001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 5, // Imported
                'category_id' => 11, // Raw Materials
                'unit_id' => 3, // Kilogram
                'purchase_unit_id' => 3,
                'sale_unit_id' => 4, // Gram
                'cost' => 25.00,
                'price' => 35.00,
                'qty' => 50,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Premium Arabica coffee beans',
            ],
            [
                'name' => 'Tea Leaves (Earl Grey)',
                'code' => 'RAW002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 5, // Imported
                'category_id' => 11, // Raw Materials
                'unit_id' => 3, // Kilogram
                'purchase_unit_id' => 3,
                'sale_unit_id' => 4, // Gram
                'cost' => 20.00,
                'price' => 30.00,
                'qty' => 20,
                'alert_quantity' => 2,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Premium Earl Grey tea leaves',
            ],
            [
                'name' => 'Milk (Fresh)',
                'code' => 'RAW003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 11, // Raw Materials
                'unit_id' => 5, // Liter
                'purchase_unit_id' => 5,
                'sale_unit_id' => 6, // Milliliter
                'cost' => 2.50,
                'price' => 4.00,
                'qty' => 100,
                'alert_quantity' => 10,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Fresh whole milk',
            ],
            [
                'name' => 'Sugar (White)',
                'code' => 'RAW004',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 11, // Raw Materials
                'unit_id' => 3, // Kilogram
                'purchase_unit_id' => 3,
                'sale_unit_id' => 4, // Gram
                'cost' => 1.50,
                'price' => 2.50,
                'qty' => 200,
                'alert_quantity' => 20,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'White granulated sugar',
            ],
            [
                'name' => 'Honey (Organic)',
                'code' => 'RAW005',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 3, // Organic Choice
                'category_id' => 11, // Raw Materials
                'unit_id' => 8, // Bottle
                'purchase_unit_id' => 8,
                'sale_unit_id' => 8,
                'cost' => 8.00,
                'price' => 12.00,
                'qty' => 30,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Organic wildflower honey',
            ],
            [
                'name' => 'Whipped Cream',
                'code' => 'RAW006',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 11, // Raw Materials
                'unit_id' => 8, // Bottle
                'purchase_unit_id' => 8,
                'sale_unit_id' => 8,
                'cost' => 3.50,
                'price' => 6.00,
                'qty' => 25,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Heavy whipped cream',
            ],
        ];

        foreach ($products as $product) {
            $createdProduct = Product::firstOrCreate(['code' => $product['code']], $product);
            
            // Add to warehouse
            Product_Warehouse::firstOrCreate([
                'product_id' => $createdProduct->id,
                'warehouse_id' => $warehouse->id,
            ], [
                'product_id' => $createdProduct->id,
                'warehouse_id' => $warehouse->id,
                'qty' => $product['qty'],
                'price' => $product['price'],
            ]);
        }

        echo "✅ Desserts, snacks, and raw materials created!\n";
        echo "🎉 All cafe sample data has been successfully created!\n";
        echo "\n📊 Summary:\n";
        echo "- Categories: Hot Beverages, Cold Beverages, Tea, Coffee, Iced Drinks, Smoothies, Pastries, Sandwiches, Desserts, Snacks, Raw Materials\n";
        echo "- Products: 30+ items including teas, coffees, food items, and raw materials\n";
        echo "- Customer Groups: Regular, VIP, Staff, Student discounts\n";
        echo "- Sample Customers: 3 customers with different groups\n";
        echo "- Suppliers: 4 suppliers for different product categories\n";
        echo "- Tables: 5 restaurant tables for dine-in service\n";
        echo "\n✅ Your cafe is ready to start selling! 🎉\n";
    }
}
