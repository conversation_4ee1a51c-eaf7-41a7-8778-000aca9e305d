<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\Tax;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\Product_Warehouse;

class CafeTeaSampleDataSeeder2 extends Seeder
{
    public function run()
    {
        $tax = Tax::where('name', 'VAT')->first();
        $warehouse = Warehouse::where('name', 'Main Warehouse')->first();

        // More Products - Cold Beverages, Food Items
        $products = [
            // Cold Beverages - Iced Drinks
            [
                'name' => 'Iced Coffee',
                'code' => 'ICE001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 1, // House Blend
                'category_id' => 5, // Iced Drinks
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.50,
                'price' => 4.00,
                'qty' => 100,
                'alert_quantity' => 10,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Cold brew coffee over ice',
            ],
            [
                'name' => 'Iced Tea',
                'code' => 'ICE002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 2, // Premium Selection
                'category_id' => 5, // Iced Drinks
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.25,
                'price' => 3.50,
                'qty' => 100,
                'alert_quantity' => 10,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Refreshing iced tea',
            ],
            [
                'name' => 'Frappuccino',
                'code' => 'ICE003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 1, // House Blend
                'category_id' => 5, // Iced Drinks
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 2.00,
                'price' => 5.50,
                'qty' => 75,
                'alert_quantity' => 8,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Blended coffee drink with ice',
            ],

            // Smoothies
            [
                'name' => 'Mango Smoothie',
                'code' => 'SMO001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 2, // Premium Selection
                'category_id' => 6, // Smoothies
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 2.50,
                'price' => 6.00,
                'qty' => 50,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Fresh mango smoothie with yogurt',
            ],
            [
                'name' => 'Berry Blast Smoothie',
                'code' => 'SMO002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 2, // Premium Selection
                'category_id' => 6, // Smoothies
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 2.75,
                'price' => 6.50,
                'qty' => 50,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Mixed berry smoothie with banana',
            ],

            // Pastries
            [
                'name' => 'Croissant',
                'code' => 'PAS001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 7, // Pastries
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 1.50,
                'price' => 3.50,
                'qty' => 30,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Buttery, flaky croissant',
            ],
            [
                'name' => 'Chocolate Muffin',
                'code' => 'PAS002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 7, // Pastries
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 1.25,
                'price' => 3.00,
                'qty' => 25,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Rich chocolate chip muffin',
            ],
            [
                'name' => 'Blueberry Muffin',
                'code' => 'PAS003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 7, // Pastries
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 1.25,
                'price' => 3.00,
                'qty' => 25,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Fresh blueberry muffin',
            ],
            [
                'name' => 'Danish Pastry',
                'code' => 'PAS004',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 7, // Pastries
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 1.75,
                'price' => 4.00,
                'qty' => 20,
                'alert_quantity' => 3,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Sweet Danish pastry with fruit',
            ],

            // Sandwiches
            [
                'name' => 'Club Sandwich',
                'code' => 'SAN001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 8, // Sandwiches
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 3.50,
                'price' => 8.50,
                'qty' => 20,
                'alert_quantity' => 3,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Triple-decker club sandwich',
            ],
            [
                'name' => 'Grilled Cheese',
                'code' => 'SAN002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 8, // Sandwiches
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 2.50,
                'price' => 6.00,
                'qty' => 25,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Classic grilled cheese sandwich',
            ],
            [
                'name' => 'Turkey Sandwich',
                'code' => 'SAN003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 4, // Local Supplier
                'category_id' => 8, // Sandwiches
                'unit_id' => 1, // Piece
                'purchase_unit_id' => 1,
                'sale_unit_id' => 1,
                'cost' => 3.00,
                'price' => 7.50,
                'qty' => 20,
                'alert_quantity' => 3,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Fresh turkey sandwich with vegetables',
            ],
        ];

        foreach ($products as $product) {
            $createdProduct = Product::firstOrCreate(['code' => $product['code']], $product);
            
            // Add to warehouse
            Product_Warehouse::firstOrCreate([
                'product_id' => $createdProduct->id,
                'warehouse_id' => $warehouse->id,
            ], [
                'product_id' => $createdProduct->id,
                'warehouse_id' => $warehouse->id,
                'qty' => $product['qty'],
                'price' => $product['price'],
            ]);
        }

        echo "✅ Cold beverages and food items created!\n";
    }
}
