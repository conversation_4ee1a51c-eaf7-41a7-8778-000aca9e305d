<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Brand;
use App\Models\Unit;
use App\Models\Tax;
use App\Models\CustomerGroup;
use App\Models\Customer;
use App\Models\Supplier;
use App\Models\Product;
use App\Models\Warehouse;
use App\Models\Product_Warehouse;
use App\Models\Table;
use Carbon\Carbon;

class CafeTeaSampleDataSeeder extends Seeder
{
    public function run()
    {
        // Create Units
        $units = [
            ['unit_name' => 'Piece', 'unit_code' => 'pc', 'base_unit' => null, 'operator' => '*', 'operation_value' => 1, 'is_active' => true],
            ['unit_name' => 'Cup', 'unit_code' => 'cup', 'base_unit' => null, 'operator' => '*', 'operation_value' => 1, 'is_active' => true],
            ['unit_name' => 'Kilogram', 'unit_code' => 'kg', 'base_unit' => null, 'operator' => '*', 'operation_value' => 1, 'is_active' => true],
            ['unit_name' => 'Gram', 'unit_code' => 'g', 'base_unit' => 3, 'operator' => '/', 'operation_value' => 1000, 'is_active' => true],
            ['unit_name' => 'Liter', 'unit_code' => 'l', 'base_unit' => null, 'operator' => '*', 'operation_value' => 1, 'is_active' => true],
            ['unit_name' => 'Milliliter', 'unit_code' => 'ml', 'base_unit' => 5, 'operator' => '/', 'operation_value' => 1000, 'is_active' => true],
            ['unit_name' => 'Box', 'unit_code' => 'box', 'base_unit' => null, 'operator' => '*', 'operation_value' => 1, 'is_active' => true],
            ['unit_name' => 'Bottle', 'unit_code' => 'bottle', 'base_unit' => null, 'operator' => '*', 'operation_value' => 1, 'is_active' => true],
        ];

        foreach ($units as $unit) {
            Unit::firstOrCreate(['unit_code' => $unit['unit_code']], $unit);
        }

        // Create Categories
        $categories = [
            ['name' => 'Hot Beverages', 'parent_id' => null, 'is_active' => true],
            ['name' => 'Cold Beverages', 'parent_id' => null, 'is_active' => true],
            ['name' => 'Tea', 'parent_id' => 1, 'is_active' => true],
            ['name' => 'Coffee', 'parent_id' => 1, 'is_active' => true],
            ['name' => 'Iced Drinks', 'parent_id' => 2, 'is_active' => true],
            ['name' => 'Smoothies', 'parent_id' => 2, 'is_active' => true],
            ['name' => 'Pastries', 'parent_id' => null, 'is_active' => true],
            ['name' => 'Sandwiches', 'parent_id' => null, 'is_active' => true],
            ['name' => 'Desserts', 'parent_id' => null, 'is_active' => true],
            ['name' => 'Snacks', 'parent_id' => null, 'is_active' => true],
            ['name' => 'Raw Materials', 'parent_id' => null, 'is_active' => true],
        ];

        foreach ($categories as $category) {
            Category::firstOrCreate(['name' => $category['name']], $category);
        }

        // Create Brands
        $brands = [
            ['title' => 'House Blend', 'image' => null, 'is_active' => true],
            ['title' => 'Premium Selection', 'image' => null, 'is_active' => true],
            ['title' => 'Organic Choice', 'image' => null, 'is_active' => true],
            ['title' => 'Local Supplier', 'image' => null, 'is_active' => true],
            ['title' => 'Imported', 'image' => null, 'is_active' => true],
        ];

        foreach ($brands as $brand) {
            Brand::firstOrCreate(['title' => $brand['title']], $brand);
        }

        // Create Tax
        $tax = Tax::firstOrCreate(
            ['name' => 'VAT'],
            ['name' => 'VAT', 'rate' => 10, 'is_active' => true]
        );

        // Create Customer Groups
        $customerGroups = [
            ['name' => 'Regular Customer', 'percentage' => '0', 'is_active' => true],
            ['name' => 'VIP Customer', 'percentage' => '10', 'is_active' => true],
            ['name' => 'Staff Discount', 'percentage' => '20', 'is_active' => true],
            ['name' => 'Student Discount', 'percentage' => '5', 'is_active' => true],
        ];

        foreach ($customerGroups as $group) {
            CustomerGroup::firstOrCreate(['name' => $group['name']], $group);
        }

        // Create Sample Customers
        $customers = [
            [
                'customer_group_id' => 1,
                'name' => 'John Smith',
                'email' => '<EMAIL>',
                'phone_number' => '+1234567890',
                'address' => '123 Main Street',
                'city' => 'Downtown',
                'country' => 'USA',
                'is_active' => true
            ],
            [
                'customer_group_id' => 2,
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'phone_number' => '+1234567891',
                'address' => '456 Oak Avenue',
                'city' => 'Downtown',
                'country' => 'USA',
                'is_active' => true
            ],
            [
                'customer_group_id' => 4,
                'name' => 'Mike Wilson',
                'email' => '<EMAIL>',
                'phone_number' => '+1234567892',
                'address' => '789 University Road',
                'city' => 'College Town',
                'country' => 'USA',
                'is_active' => true
            ],
        ];

        foreach ($customers as $customer) {
            Customer::firstOrCreate(['email' => $customer['email']], $customer);
        }

        // Create Suppliers
        $suppliers = [
            [
                'name' => 'Coffee Bean Suppliers',
                'company_name' => 'Premium Coffee Co.',
                'email' => '<EMAIL>',
                'phone_number' => '+1555000001',
                'address' => '100 Coffee Street',
                'city' => 'Bean City',
                'country' => 'Colombia',
                'is_active' => true
            ],
            [
                'name' => 'Tea Leaf Importers',
                'company_name' => 'Global Tea Ltd.',
                'email' => '<EMAIL>',
                'phone_number' => '+1555000002',
                'address' => '200 Tea Garden Road',
                'city' => 'Darjeeling',
                'country' => 'India',
                'is_active' => true
            ],
            [
                'name' => 'Local Bakery',
                'company_name' => 'Fresh Bakes Inc.',
                'email' => '<EMAIL>',
                'phone_number' => '+1555000003',
                'address' => '300 Bakery Lane',
                'city' => 'Downtown',
                'country' => 'USA',
                'is_active' => true
            ],
            [
                'name' => 'Dairy Products',
                'company_name' => 'Farm Fresh Dairy',
                'email' => '<EMAIL>',
                'phone_number' => '+1555000004',
                'address' => '400 Farm Road',
                'city' => 'Countryside',
                'country' => 'USA',
                'is_active' => true
            ],
        ];

        foreach ($suppliers as $supplier) {
            Supplier::firstOrCreate(['email' => $supplier['email']], $supplier);
        }

        // Create Warehouse
        $warehouse = Warehouse::firstOrCreate(
            ['name' => 'Main Warehouse'],
            [
                'name' => 'Main Warehouse',
                'phone' => '+1234567890',
                'email' => '<EMAIL>',
                'address' => 'Main Storage Facility',
                'is_active' => true
            ]
        );

        // Create Tables for Restaurant
        $tables = [
            ['name' => 'Table 1', 'number_of_person' => 2, 'description' => 'Window table for 2', 'is_active' => true],
            ['name' => 'Table 2', 'number_of_person' => 4, 'description' => 'Center table for 4', 'is_active' => true],
            ['name' => 'Table 3', 'number_of_person' => 6, 'description' => 'Large table for 6', 'is_active' => true],
            ['name' => 'Counter', 'number_of_person' => 1, 'description' => 'Counter seating', 'is_active' => true],
            ['name' => 'Outdoor 1', 'number_of_person' => 4, 'description' => 'Outdoor patio table', 'is_active' => true],
        ];

        foreach ($tables as $table) {
            Table::firstOrCreate(['name' => $table['name']], $table);
        }

        echo "✅ Basic setup completed! Creating products...\n";

        // Create Sample Products
        $products = [
            // Hot Beverages - Tea
            [
                'name' => 'Earl Grey Tea',
                'code' => 'TEA001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 2, // Premium Selection
                'category_id' => 3, // Tea
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.50,
                'price' => 3.50,
                'qty' => 100,
                'alert_quantity' => 10,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Classic Earl Grey tea with bergamot',
            ],
            [
                'name' => 'Green Tea',
                'code' => 'TEA002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 3, // Organic Choice
                'category_id' => 3, // Tea
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.25,
                'price' => 3.00,
                'qty' => 100,
                'alert_quantity' => 10,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Organic green tea with antioxidants',
            ],
            [
                'name' => 'Chamomile Tea',
                'code' => 'TEA003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 3, // Organic Choice
                'category_id' => 3, // Tea
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.75,
                'price' => 4.00,
                'qty' => 50,
                'alert_quantity' => 5,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Relaxing chamomile herbal tea',
            ],

            // Hot Beverages - Coffee
            [
                'name' => 'Espresso',
                'code' => 'COF001',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 1, // House Blend
                'category_id' => 4, // Coffee
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.00,
                'price' => 2.50,
                'qty' => 200,
                'alert_quantity' => 20,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Strong espresso shot',
            ],
            [
                'name' => 'Cappuccino',
                'code' => 'COF002',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 1, // House Blend
                'category_id' => 4, // Coffee
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.50,
                'price' => 4.50,
                'qty' => 150,
                'alert_quantity' => 15,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Espresso with steamed milk and foam',
            ],
            [
                'name' => 'Latte',
                'code' => 'COF003',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 1, // House Blend
                'category_id' => 4, // Coffee
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.75,
                'price' => 5.00,
                'qty' => 150,
                'alert_quantity' => 15,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Espresso with steamed milk',
            ],
            [
                'name' => 'Americano',
                'code' => 'COF004',
                'type' => 'standard',
                'barcode_symbology' => 'C128',
                'brand_id' => 1, // House Blend
                'category_id' => 4, // Coffee
                'unit_id' => 2, // Cup
                'purchase_unit_id' => 2,
                'sale_unit_id' => 2,
                'cost' => 1.25,
                'price' => 3.50,
                'qty' => 150,
                'alert_quantity' => 15,
                'tax_id' => $tax->id,
                'tax_method' => 1,
                'is_active' => true,
                'product_details' => 'Espresso with hot water',
            ],
        ];

        foreach ($products as $product) {
            $createdProduct = Product::firstOrCreate(['code' => $product['code']], $product);

            // Add to warehouse
            Product_Warehouse::firstOrCreate([
                'product_id' => $createdProduct->id,
                'warehouse_id' => $warehouse->id,
            ], [
                'product_id' => $createdProduct->id,
                'warehouse_id' => $warehouse->id,
                'qty' => $product['qty'],
                'price' => $product['price'],
            ]);
        }

        echo "✅ Hot beverages created!\n";
    }
}
