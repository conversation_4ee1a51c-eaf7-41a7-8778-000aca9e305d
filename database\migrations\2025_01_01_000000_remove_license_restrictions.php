<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove expiry date restrictions from general_settings
        DB::table('general_settings')->update([
            'expiry_date' => null,
            'expiry_type' => 'unlimited',
            'expiry_value' => '0'
        ]);
        
        echo "✅ License restrictions removed successfully!\n";
        echo "🎉 Your system will now work without any expiry date limitations.\n";
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This migration is irreversible as we're removing license restrictions
        echo "⚠️ Cannot reverse license restriction removal.\n";
    }
};
