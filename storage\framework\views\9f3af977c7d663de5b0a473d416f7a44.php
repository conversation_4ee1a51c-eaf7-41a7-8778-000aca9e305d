
<?php $__env->startPush('css'); ?>

<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>

    <?php if(count($languages)): ?>
    <div class="container-fluid mt-3 mb-3">
        <div class="card w-1/2">

            <div class="card-header d-flex justify-content-between">

                <?php echo e(__('translation::translation.languages')); ?>



                <a href="<?php echo e(route('languages.create')); ?>" class="btn btn-default">
                    <?php echo e(__('translation::translation.add')); ?>

                </a>

            </div>

            <div class="card-body">
            <div class="table-responsive">
                <table class="table ">

                    <thead>
                        <tr>
                            <th><?php echo e(__('translation::translation.language_name')); ?></th>
                            <th><?php echo e(__('translation::translation.locale')); ?></th>
                        </tr>
                    </thead>

                    <tbody>
                        <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $language => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td>
                                    <?php echo e($name); ?>

                                </td>
                                <td>
                                    <a href="<?php echo e(route('languages.translations.index', $language)); ?>">
                                        <?php echo e($language); ?>

                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
            </div>

        </div>
    </div>
    <?php endif; ?>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('backend.layout.main', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\sale\resources\views/vendor/translation/languages/index.blade.php ENDPATH**/ ?>