<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cafe Sample Data - Quick Import Links</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .section h3 {
            color: #654321;
            margin-top: 0;
        }
        .download-btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #8B4513;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .download-btn:hover {
            background-color: #A0522D;
        }
        .import-link {
            display: inline-block;
            padding: 8px 16px;
            background-color: #228B22;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
            font-size: 14px;
        }
        .import-link:hover {
            background-color: #32CD32;
        }
        .step {
            background-color: #e8f4f8;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #2196F3;
        }
        .warning {
            background-color: #fff3cd;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }
        .file-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>☕ Cafe Sample Data - Quick Import Guide</h1>
        
        <div class="success">
            <strong>🎉 Ready to Test!</strong> All sample data files are prepared for your cafe management system.
        </div>

        <div class="warning">
            <strong>⚠️ Important:</strong> Import files in the correct order: Customer Groups → Categories → Brands → Suppliers → Products → Customers
        </div>

        <div class="section">
            <h3>📋 Step 1: Customer Groups</h3>
            <p>Import customer groups with discount percentages first.</p>
            <a href="cafe_customer_groups_sample.csv" class="download-btn" download>📥 Download Customer Groups CSV</a>
            <a href="../customer_group" class="import-link" target="_blank">🔗 Go to Import Page</a>
            <div class="file-info">Contains: Regular, VIP, Staff, Student discounts (10 groups)</div>
        </div>

        <div class="section">
            <h3>📂 Step 2: Categories</h3>
            <p>Import product categories to organize your menu items.</p>
            <a href="cafe_categories_sample.csv" class="download-btn" download>📥 Download Categories CSV</a>
            <a href="../category" class="import-link" target="_blank">🔗 Go to Import Page</a>
            <div class="file-info">Contains: Hot Beverages, Cold Beverages, Food Items, etc.</div>
        </div>

        <div class="section">
            <h3>🏷️ Step 3: Brands</h3>
            <p>Import brand names for your products.</p>
            <a href="cafe_brands_sample.csv" class="download-btn" download>📥 Download Brands CSV</a>
            <a href="../brand" class="import-link" target="_blank">🔗 Go to Import Page</a>
            <div class="file-info">Contains: House Blend, Premium Selection, Organic Choice (15 brands)</div>
        </div>

        <div class="section">
            <h3>🏪 Step 4: Suppliers</h3>
            <p>Import suppliers for purchasing and inventory management.</p>
            <a href="cafe_suppliers_sample.csv" class="download-btn" download>📥 Download Suppliers CSV</a>
            <a href="../supplier" class="import-link" target="_blank">🔗 Go to Import Page</a>
            <div class="file-info">Contains: Coffee suppliers, Tea importers, Bakeries (10 suppliers)</div>
        </div>

        <div class="section">
            <h3>🍵 Step 5: Products</h3>
            <p>Import your complete cafe menu with prices and details.</p>
            <a href="cafe_products_sample.csv" class="download-btn" download>📥 Download Products CSV</a>
            <a href="../products" class="import-link" target="_blank">🔗 Go to Import Page</a>
            <div class="file-info">Contains: 40+ items - Teas, Coffees, Food, Desserts with realistic pricing</div>
        </div>

        <div class="section">
            <h3>👥 Step 6: Customers</h3>
            <p>Import sample customers for testing sales and loyalty programs.</p>
            <a href="cafe_customers_sample.csv" class="download-btn" download>📥 Download Customers CSV</a>
            <a href="../customer" class="import-link" target="_blank">🔗 Go to Import Page</a>
            <div class="file-info">Contains: 20 customers with different groups and contact info</div>
        </div>

        <div class="section">
            <h3>🎯 Quick Testing Ideas</h3>
            <div class="step">
                <strong>POS Testing:</strong> Create sales with different customer groups to test discount calculations
            </div>
            <div class="step">
                <strong>Inventory:</strong> Create purchase orders from suppliers and track stock levels
            </div>
            <div class="step">
                <strong>Reports:</strong> Generate sales reports, profit analysis, and customer analytics
            </div>
            <div class="step">
                <strong>Table Service:</strong> Use the restaurant tables for dine-in orders
            </div>
        </div>

        <div class="section">
            <h3>📊 Sample Data Summary</h3>
            <ul>
                <li><strong>Products:</strong> 40+ cafe items with realistic pricing</li>
                <li><strong>Categories:</strong> Organized menu structure</li>
                <li><strong>Customer Groups:</strong> VIP (10%), Staff (20%), Student (5%) discounts</li>
                <li><strong>Suppliers:</strong> Coffee, Tea, Bakery, Dairy suppliers</li>
                <li><strong>Customers:</strong> 20 test customers with various groups</li>
                <li><strong>Price Range:</strong> $2.50 - $8.50 (typical cafe pricing)</li>
            </ul>
        </div>

        <div class="success">
            <strong>🚀 Alternative:</strong> You can also run the database seeder: <code>php artisan db:seed --class=CafeSampleDataSeeder</code>
        </div>

        <div class="section">
            <h3>📞 Need Help?</h3>
            <p>If you encounter any issues:</p>
            <ol>
                <li>Check that files are imported in the correct order</li>
                <li>Verify CSV file formats (UTF-8 encoding)</li>
                <li>Ensure all required fields are filled</li>
                <li>Check import logs for specific error messages</li>
            </ol>
        </div>
    </div>
</body>
</html>
