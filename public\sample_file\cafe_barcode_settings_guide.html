<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cafe Barcode Settings Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        .setting-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            background-color: #fafafa;
        }
        .setting-card.default {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        .setting-name {
            color: #654321;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .setting-description {
            color: #666;
            margin-bottom: 15px;
            font-style: italic;
        }
        .specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .spec-item {
            background: white;
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid #8B4513;
        }
        .spec-label {
            font-weight: bold;
            color: #333;
        }
        .spec-value {
            color: #666;
        }
        .default-badge {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .continuous-badge {
            background: #17a2b8;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .usage-examples {
            background: #e8f4f8;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
        }
        .usage-title {
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 8px;
        }
        .quick-actions {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f0f8ff;
            border-radius: 8px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background: #8B4513;
            color: white;
        }
        .btn-primary:hover {
            background: #A0522D;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-success:hover {
            background: #218838;
        }
        .info-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏷️ Cafe Barcode Settings Guide</h1>
        
        <div class="info-box">
            <strong>✅ Ready to Use!</strong> Your cafe now has 8 pre-configured barcode label settings optimized for different cafe products and use cases.
        </div>

        <div class="quick-actions">
            <h3>🚀 Quick Actions</h3>
            <a href="../barcodes" class="btn btn-primary">📋 View All Settings</a>
            <a href="../barcodes/create" class="btn btn-success">➕ Create New Setting</a>
            <a href="../products/print-barcode" class="btn btn-primary">🖨️ Print Barcodes</a>
        </div>

        <h2>📋 Available Barcode Settings</h2>

        <div class="setting-card default">
            <div class="setting-name">
                ☕ Cafe Standard Labels 
                <span class="default-badge">DEFAULT</span>
            </div>
            <div class="setting-description">
                Standard 2x1 inch labels for cafe products - 30 labels per sheet (Letter size)
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">2.0" × 1.0"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Paper Size:</div>
                    <div class="spec-value">8.5" × 11"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Labels per Row:</div>
                    <div class="spec-value">3</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Total per Sheet:</div>
                    <div class="spec-value">30</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Coffee cups, tea boxes, pastries, sandwiches, general cafe products
            </div>
        </div>

        <div class="setting-card">
            <div class="setting-name">🏷️ Small Product Labels</div>
            <div class="setting-description">
                Small 1.5x0.75 inch labels for small items like tea bags, sugar packets - 40 labels per sheet
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">1.5" × 0.75"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Labels per Row:</div>
                    <div class="spec-value">4</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Total per Sheet:</div>
                    <div class="spec-value">40</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Tea bags, sugar packets, small snacks, condiment containers, spice jars
            </div>
        </div>

        <div class="setting-card">
            <div class="setting-name">📋 Large Menu Labels</div>
            <div class="setting-description">
                Large 3x2 inch labels for menu boards and large containers - 8 labels per sheet
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">3.0" × 2.0"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Labels per Row:</div>
                    <div class="spec-value">2</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Total per Sheet:</div>
                    <div class="spec-value">8</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Menu boards, large storage containers, bulk items, promotional displays
            </div>
        </div>

        <div class="setting-card">
            <div class="setting-name">💰 Price Tag Labels</div>
            <div class="setting-description">
                Square 1x1 inch price tags for display cases and shelves - 48 labels per sheet
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">1.0" × 1.0"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Labels per Row:</div>
                    <div class="spec-value">6</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Total per Sheet:</div>
                    <div class="spec-value">48</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Display case items, shelf pricing, small product tags, dessert labels
            </div>
        </div>

        <div class="setting-card">
            <div class="setting-name">🍼 Bottle/Cup Labels</div>
            <div class="setting-description">
                Narrow 2.5x1 inch labels perfect for bottles, cups, and cylindrical containers - 20 labels per sheet
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">2.5" × 1.0"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Labels per Row:</div>
                    <div class="spec-value">3</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Total per Sheet:</div>
                    <div class="spec-value">20</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Water bottles, juice containers, coffee cups, smoothie cups, cylindrical items
            </div>
        </div>

        <div class="setting-card">
            <div class="setting-name">📦 Inventory Labels</div>
            <div class="setting-description">
                Medium 2x1.5 inch labels for inventory management and storage containers - 15 labels per sheet
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">2.0" × 1.5"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Labels per Row:</div>
                    <div class="spec-value">3</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Total per Sheet:</div>
                    <div class="spec-value">15</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Storage containers, inventory tracking, warehouse labels, bulk items
            </div>
        </div>

        <div class="setting-card">
            <div class="setting-name">
                🖨️ Thermal Roll Labels 
                <span class="continuous-badge">CONTINUOUS</span>
            </div>
            <div class="setting-description">
                Continuous thermal roll labels 2x1 inch for thermal printers - 28 labels per roll
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">2.0" × 1.0"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Roll Width:</div>
                    <div class="spec-value">2.25"</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Type:</div>
                    <div class="spec-value">Continuous Feed</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Thermal printers, receipt printers, high-volume labeling, POS integration
            </div>
        </div>

        <div class="setting-card">
            <div class="setting-name">⭕ Round Labels</div>
            <div class="setting-description">
                Round 1.5 inch diameter labels for jars and round containers - 24 labels per sheet
            </div>
            <div class="specs">
                <div class="spec-item">
                    <div class="spec-label">Label Size:</div>
                    <div class="spec-value">1.5" diameter</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Labels per Row:</div>
                    <div class="spec-value">4</div>
                </div>
                <div class="spec-item">
                    <div class="spec-label">Total per Sheet:</div>
                    <div class="spec-value">24</div>
                </div>
            </div>
            <div class="usage-examples">
                <div class="usage-title">Perfect for:</div>
                Jam jars, honey containers, round tins, coffee bean containers, spice jars
            </div>
        </div>

        <div class="warning-box">
            <strong>💡 Pro Tips:</strong>
            <ul>
                <li>The "Cafe Standard Labels" is set as default and will be used automatically</li>
                <li>You can change the default setting by editing any barcode setting</li>
                <li>Thermal roll labels work best with thermal printers</li>
                <li>Test print a few labels before printing large batches</li>
                <li>Adjust margins if labels don't align perfectly with your printer</li>
            </ul>
        </div>

        <h2>🖨️ How to Print Barcodes</h2>
        <div class="info-box">
            <ol>
                <li><strong>Go to Products → Print Barcode</strong></li>
                <li><strong>Select products</strong> you want to print labels for</li>
                <li><strong>Choose barcode setting</strong> (default: Cafe Standard Labels)</li>
                <li><strong>Set quantity</strong> for each product</li>
                <li><strong>Click "Print"</strong> to generate PDF</li>
                <li><strong>Print the PDF</strong> on appropriate label sheets</li>
            </ol>
        </div>

        <div class="quick-actions">
            <h3>🎯 Ready to Start Labeling!</h3>
            <p>Your cafe barcode system is fully configured. Choose the right label size for each product type and start printing professional labels for your cafe!</p>
        </div>
    </div>
</body>
</html>
