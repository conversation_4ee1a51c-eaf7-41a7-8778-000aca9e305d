<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Background Customization Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #8B4513;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step-number {
            background: #8B4513;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .step-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .file-path {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            border-left: 3px solid #007bff;
        }
        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .tip {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .current-files {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .file-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        .file-name {
            font-weight: bold;
            color: #495057;
            margin-bottom: 8px;
        }
        .file-usage {
            font-size: 12px;
            color: #6c757d;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .btn-primary {
            background: #8B4513;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .quick-actions {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f0f8ff;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Background Customization Guide</h1>
        
        <div class="quick-actions">
            <h3>📁 Current Background Files Location</h3>
            <div class="file-path">C:\laragon\www\sale\public\images\auth\</div>
            <p>Your project uses different backgrounds for light and dark modes</p>
        </div>

        <h2>📋 Current Background Files</h2>
        <div class="current-files">
            <div class="file-card">
                <div class="file-name">🌞 auth-light.jpg</div>
                <div class="file-usage">Used for: Light mode login/register pages</div>
            </div>
            <div class="file-card">
                <div class="file-name">🌙 auth-dark.jpg</div>
                <div class="file-usage">Used for: Dark mode login/register pages</div>
            </div>
        </div>

        <h2>🔧 How to Change Backgrounds</h2>

        <div class="step">
            <div class="step-title">
                <span class="step-number">1</span>
                Prepare Your Images
            </div>
            <div class="step-content">
                <strong>Image Requirements:</strong>
                <ul>
                    <li><strong>Format:</strong> JPG, PNG, or WebP</li>
                    <li><strong>Recommended Size:</strong> 1920x1080 or higher</li>
                    <li><strong>Aspect Ratio:</strong> 16:9 works best</li>
                    <li><strong>File Size:</strong> Under 2MB for faster loading</li>
                </ul>
                
                <div class="tip">
                    <strong>💡 Tip:</strong> Choose images that work well as backgrounds - not too busy or distracting, with good contrast for the login form.
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">2</span>
                Replace the Image Files
            </div>
            <div class="step-content">
                <strong>Navigate to the auth folder:</strong>
                <div class="file-path">C:\laragon\www\sale\public\images\auth\</div>
                
                <strong>Replace these files:</strong>
                <ul>
                    <li><strong>auth-light.jpg</strong> - Your light mode background</li>
                    <li><strong>auth-dark.jpg</strong> - Your dark mode background</li>
                </ul>
                
                <div class="warning">
                    <strong>⚠️ Important:</strong> Keep the same file names (auth-light.jpg and auth-dark.jpg) or you'll need to update the CSS as well.
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">3</span>
                Alternative: Use Different File Names
            </div>
            <div class="step-content">
                <strong>If you want to use different file names:</strong>
                
                <p>1. Upload your images to the auth folder with any name:</p>
                <div class="code-block">
                C:\laragon\www\sale\public\images\auth\my-cafe-bg.jpg
                C:\laragon\www\sale\public\images\auth\my-cafe-dark.jpg
                </div>
                
                <p>2. Update the CSS file:</p>
                <div class="file-path">C:\laragon\www\sale\public\css\auth.css</div>
                
                <p>3. Change these lines:</p>
                <div class="code-block">
                /* Light mode background */
                .login-page,
                .register-page {
                    background-image: url('../images/auth/my-cafe-bg.jpg');
                }

                /* Dark mode background */
                .dark-mode .login-page,
                .dark-mode .register-page {
                    background-image: url('../images/auth/my-cafe-dark.jpg');
                }
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">4</span>
                Test Your Changes
            </div>
            <div class="step-content">
                <strong>After replacing the images:</strong>
                <ol>
                    <li>Clear your browser cache (Ctrl+F5 or Cmd+Shift+R)</li>
                    <li>Visit the login page: <code>http://localhost/sale/login</code></li>
                    <li>Check both light and dark modes</li>
                    <li>Verify the background displays correctly</li>
                </ol>
                
                <div class="tip">
                    <strong>💡 Pro Tip:</strong> If changes don't appear immediately, try opening the page in an incognito/private browser window.
                </div>
            </div>
        </div>

        <h2>🎨 Background Customization Options</h2>

        <div class="step">
            <div class="step-title">
                <span class="step-number">5</span>
                Advanced CSS Customizations
            </div>
            <div class="step-content">
                <strong>You can also modify these CSS properties in auth.css:</strong>
                
                <div class="code-block">
                .login-page,
                .register-page {
                    background: #f5f5f5;                    /* Fallback color */
                    background-image: url('../images/auth/auth-light.jpg');
                    background-size: cover;                 /* Options: cover, contain, auto */
                    background-position: center center;     /* Position the image */
                    background-repeat: no-repeat;          /* Don't repeat the image */
                    background-attachment: fixed;          /* Keep background fixed while scrolling */
                }
                </div>
                
                <strong>Background Size Options:</strong>
                <ul>
                    <li><strong>cover:</strong> Scales image to cover entire area (may crop)</li>
                    <li><strong>contain:</strong> Scales image to fit entirely (may show borders)</li>
                    <li><strong>100% 100%:</strong> Stretches image to exact dimensions</li>
                </ul>
            </div>
        </div>

        <h2>☕ Cafe-Themed Background Suggestions</h2>

        <div class="tip">
            <strong>🎨 Great Background Ideas for Your Cafe:</strong>
            <ul>
                <li><strong>Coffee beans pattern</strong> - Subtle and professional</li>
                <li><strong>Cafe interior</strong> - Warm and inviting atmosphere</li>
                <li><strong>Coffee cup with steam</strong> - Classic cafe imagery</li>
                <li><strong>Wooden texture</strong> - Natural, warm feeling</li>
                <li><strong>Chalkboard menu style</strong> - Authentic cafe look</li>
                <li><strong>Latte art patterns</strong> - Artistic and coffee-focused</li>
            </ul>
        </div>

        <h2>🔧 Quick File Replacement Steps</h2>

        <div class="step">
            <div class="step-title">
                Easy Method (Keep Same Names)
            </div>
            <div class="step-content">
                <ol>
                    <li>Find your new background images</li>
                    <li>Rename them to:
                        <ul>
                            <li><code>auth-light.jpg</code> (for light mode)</li>
                            <li><code>auth-dark.jpg</code> (for dark mode)</li>
                        </ul>
                    </li>
                    <li>Copy them to: <code>C:\laragon\www\sale\public\images\auth\</code></li>
                    <li>Replace the existing files</li>
                    <li>Refresh your browser and check the login page</li>
                </ol>
            </div>
        </div>

        <div class="warning">
            <strong>🔄 Backup Tip:</strong> Before replacing, consider backing up the original files by renaming them to <code>auth-light-original.jpg</code> and <code>auth-dark-original.jpg</code>
        </div>

        <div class="quick-actions">
            <h3>🎉 Ready to Customize!</h3>
            <p>Your background images are located in the auth folder. Simply replace them with your preferred cafe-themed backgrounds!</p>
            <a href="../login" class="btn btn-primary">🔐 View Login Page</a>
        </div>
    </div>
</body>
</html>
