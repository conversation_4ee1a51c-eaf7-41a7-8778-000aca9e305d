# 🍵 Cafe Sample Data Files Guide

## 📁 Available Sample Data Files

This folder contains comprehensive sample data files specifically designed for **Cafe/Tea business** testing and setup.

### 📋 File List:

1. **`cafe_products_sample.csv`** - 40+ cafe products
2. **`cafe_customers_sample.csv`** - 20 sample customers
3. **`cafe_suppliers_sample.csv`** - 10 suppliers
4. **`cafe_customer_groups_sample.csv`** - Customer groups with discounts
5. **`cafe_categories_sample.csv`** - Product categories
6. **`cafe_brands_sample.csv`** - Brand names

---

## 🚀 How to Import Sample Data

### 1. **Products Import**
- **Go to:** Products → Import Product
- **Upload:** `cafe_products_sample.csv`
- **Contains:** 40+ items including:
  - ☕ Hot beverages (Tea, Coffee)
  - 🧊 Cold beverages (Iced drinks, Smoothies)
  - 🥐 Food items (Pastries, Sandwiches, Desserts)
  - 🍿 Snacks

### 2. **Customers Import**
- **Go to:** People → Customer → Import Customer
- **Upload:** `cafe_customers_sample.csv`
- **Contains:** 20 customers with different groups

### 3. **Suppliers Import**
- **Go to:** People → Supplier → Import Supplier
- **Upload:** `cafe_suppliers_sample.csv`
- **Contains:** 10 suppliers for different product categories

### 4. **Customer Groups Import**
- **Go to:** Settings → Customer Group → Import Customer Group
- **Upload:** `cafe_customer_groups_sample.csv`
- **Contains:** Various discount groups (VIP, Student, Staff, etc.)

### 5. **Categories Import**
- **Go to:** Products → Category → Import Category
- **Upload:** `cafe_categories_sample.csv`
- **Contains:** Organized category structure

### 6. **Brands Import**
- **Go to:** Settings → Brand → Import Brand
- **Upload:** `cafe_brands_sample.csv`
- **Contains:** 15 brand names

---

## 📊 Sample Data Overview

### 🍵 **Products (40+ items)**

#### Hot Beverages:
- **Teas:** Earl Grey ($3.50), Green Tea ($3.00), Chamomile ($4.00)
- **Coffees:** Espresso ($2.50), Cappuccino ($4.50), Latte ($5.00)

#### Cold Beverages:
- **Iced Drinks:** Iced Coffee ($4.00), Frappuccino ($5.50)
- **Smoothies:** Mango ($6.00), Berry Blast ($6.50)

#### Food Items:
- **Pastries:** Croissant ($3.50), Muffins ($3.00)
- **Sandwiches:** Club Sandwich ($8.50), Grilled Cheese ($6.00)
- **Desserts:** Chocolate Cake ($6.50), Cheesecake ($7.50)

### 👥 **Customer Groups**
- **Regular Customer** (0% discount)
- **VIP Customer** (10% discount)
- **Staff Discount** (20% discount)
- **Student Discount** (5% discount)
- **Senior Citizen** (15% discount)

### 🏪 **Suppliers**
- **Premium Coffee Co.** - Coffee beans
- **Global Tea Ltd.** - Tea leaves
- **Fresh Bakes Inc.** - Pastries & food
- **Farm Fresh Dairy** - Dairy products

---

## 🎯 Quick Setup Steps

### **Option 1: Import All Files (Recommended)**
1. Import customer groups first
2. Import categories
3. Import brands
4. Import suppliers
5. Import products
6. Import customers

### **Option 2: Use Database Seeder**
```bash
php artisan db:seed --class=CafeSampleDataSeeder
```

---

## 💡 Testing Scenarios

### **POS Testing:**
1. Create sales with different customer groups
2. Test discount calculations
3. Try table service orders
4. Process different payment methods

### **Inventory Testing:**
1. Create purchase orders from suppliers
2. Test stock alerts
3. Track product movements
4. Generate inventory reports

### **Customer Management:**
1. Add customers to different groups
2. Test loyalty programs
3. Track customer purchase history
4. Send promotional emails

---

## 📈 Sample Pricing Structure

| Category | Price Range | Examples |
|----------|-------------|----------|
| **Tea** | $3.00 - $4.00 | Earl Grey, Green Tea |
| **Coffee** | $2.50 - $5.50 | Espresso, Latte, Mocha |
| **Cold Drinks** | $3.50 - $6.75 | Iced Coffee, Smoothies |
| **Pastries** | $2.50 - $4.00 | Croissant, Muffins |
| **Sandwiches** | $6.00 - $8.50 | Grilled Cheese, Club |
| **Desserts** | $3.00 - $8.50 | Ice Cream, Tiramisu |

---

## 🔧 Customization Tips

### **Modify Prices:**
- Edit CSV files before importing
- Adjust cost and selling prices
- Update based on local market

### **Add More Products:**
- Follow the CSV format
- Use consistent naming
- Include proper categories

### **Localize Data:**
- Change addresses to your location
- Update phone number formats
- Modify currency if needed

---

## ⚠️ Important Notes

1. **Import Order Matters:** Import customer groups and categories before products
2. **Backup First:** Always backup your database before importing
3. **Test Environment:** Try imports in test environment first
4. **Data Validation:** Check imported data for accuracy
5. **Stock Levels:** Adjust initial stock quantities as needed

---

## 🆘 Troubleshooting

### **Import Errors:**
- Check CSV format and encoding (UTF-8)
- Ensure required fields are filled
- Verify category/brand names exist

### **Missing Data:**
- Import dependencies first (categories, brands)
- Check file permissions
- Verify file paths

### **Performance:**
- Import in smaller batches if needed
- Clear cache after imports
- Rebuild search indexes

---

## 📞 Support

If you need help with the sample data:
1. Check the import logs for errors
2. Verify CSV file formats
3. Ensure all dependencies are imported
4. Contact support with specific error messages

---

**🎉 Happy Testing! Your cafe management system is ready to go!** ☕
