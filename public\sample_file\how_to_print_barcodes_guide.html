<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>How to Print Barcodes - Step by Step Guide</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8B4513;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f8f9fa;
            border-left: 4px solid #8B4513;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .step-number {
            background: #8B4513;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .step-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .step-content {
            color: #666;
            line-height: 1.6;
        }
        .screenshot {
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 15px 0;
            padding: 10px;
            background: #f9f9f9;
            text-align: center;
            font-style: italic;
            color: #666;
        }
        .tip {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .btn-primary {
            background: #8B4513;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .quick-actions {
            text-align: center;
            margin: 30px 0;
            padding: 20px;
            background: #f0f8ff;
            border-radius: 8px;
        }
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .feature-item {
            background: white;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .feature-title {
            font-weight: bold;
            color: #8B4513;
            margin-bottom: 8px;
        }
        .code-example {
            background: #f4f4f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖨️ How to Print Barcodes - Complete Guide</h1>
        
        <div class="quick-actions">
            <h3>🚀 Quick Access</h3>
            <a href="../products/print_barcode" class="btn btn-primary">🖨️ Go to Print Barcode Page</a>
            <a href="../products" class="btn btn-success">📦 View All Products</a>
        </div>

        <div class="warning">
            <strong>⚠️ Before You Start:</strong> Make sure you have products in your system. If you don't have products yet, go to Products → Add Product or import the sample data first.
        </div>

        <h2>📋 Step-by-Step Instructions</h2>

        <div class="step">
            <div class="step-title">
                <span class="step-number">1</span>
                Access the Print Barcode Page
            </div>
            <div class="step-content">
                <strong>Go to:</strong> Products → Print Barcode<br>
                <strong>URL:</strong> <code>http://localhost/sale/products/print_barcode</code>
                <div class="screenshot">
                    [Screenshot: Navigation menu showing Products → Print Barcode]
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">2</span>
                Add Products to Print
            </div>
            <div class="step-content">
                <strong>Method 1 - Search and Add:</strong>
                <ul>
                    <li>Click in the search box that says "Please type product code and select..."</li>
                    <li>Type product name or code (e.g., "Earl Grey Tea" or "TEA001")</li>
                    <li>Select the product from the dropdown</li>
                    <li>Product will be added to the table below</li>
                </ul>
                
                <strong>Method 2 - Pre-loaded Products:</strong>
                <ul>
                    <li>If you have sample data, some products may already be loaded in the table</li>
                    <li>You can adjust quantities or remove products as needed</li>
                </ul>

                <div class="tip">
                    <strong>💡 Tip:</strong> You can add multiple products by repeating the search process. Each product will appear as a new row in the table.
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">3</span>
                Set Quantities
            </div>
            <div class="step-content">
                <ul>
                    <li>In the table, find the "Quantity" column</li>
                    <li>Change the number to how many labels you want for each product</li>
                    <li>Default is 1 label per product</li>
                    <li>You can set different quantities for different products</li>
                </ul>
                
                <div class="code-example">
                    Example:
                    - Earl Grey Tea: 5 labels
                    - Cappuccino: 10 labels  
                    - Croissant: 3 labels
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">4</span>
                Choose What to Print on Labels
            </div>
            <div class="step-content">
                <strong>Information on Label section:</strong>
                <div class="feature-list">
                    <div class="feature-item">
                        <div class="feature-title">✅ Product Name</div>
                        Shows the product name on the label
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">✅ Price</div>
                        Shows the selling price
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">✅ Promotional Price</div>
                        Shows special/discount price if available
                    </div>
                    <div class="feature-item">
                        <div class="feature-title">✅ Business Name</div>
                        Shows your cafe name on the label
                    </div>
                </div>
                
                <div class="tip">
                    <strong>💡 Customization:</strong> You can uncheck any items you don't want on the labels and adjust the font sizes for each element.
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">5</span>
                Select Paper Size/Barcode Setting
            </div>
            <div class="step-content">
                <strong>Paper Size dropdown options:</strong>
                <ul>
                    <li><strong>Cafe Standard Labels</strong> (2×1 inch) - DEFAULT ⭐</li>
                    <li><strong>Small Product Labels</strong> (1.5×0.75 inch)</li>
                    <li><strong>Large Menu Labels</strong> (3×2 inch)</li>
                    <li><strong>Price Tag Labels</strong> (1×1 inch)</li>
                    <li><strong>Bottle/Cup Labels</strong> (2.5×1 inch)</li>
                    <li><strong>And more...</strong></li>
                </ul>
                
                <div class="tip">
                    <strong>💡 Recommendation:</strong> Use "Cafe Standard Labels" for most cafe products. It's optimized for coffee cups, tea boxes, and food items.
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">
                <span class="step-number">6</span>
                Generate and Print
            </div>
            <div class="step-content">
                <ol>
                    <li><strong>Click "Submit"</strong> button at the bottom</li>
                    <li><strong>Preview window opens</strong> showing your labels</li>
                    <li><strong>Review the layout</strong> - check if everything looks correct</li>
                    <li><strong>Click "Print"</strong> button in the preview window</li>
                    <li><strong>Print dialog opens</strong> - select your printer and print</li>
                </ol>
                
                <div class="warning">
                    <strong>⚠️ Important:</strong> Make sure you have the correct label sheets loaded in your printer that match the selected paper size.
                </div>
            </div>
        </div>

        <h2>🎯 Common Use Cases</h2>

        <div class="feature-list">
            <div class="feature-item">
                <div class="feature-title">☕ Coffee Products</div>
                Use Standard Labels (2×1") for coffee cups, beans, and accessories
            </div>
            <div class="feature-item">
                <div class="feature-title">🍵 Tea Products</div>
                Use Small Labels (1.5×0.75") for tea bags and boxes
            </div>
            <div class="feature-item">
                <div class="feature-title">🥐 Food Items</div>
                Use Standard Labels for pastries, sandwiches, and packaged food
            </div>
            <div class="feature-item">
                <div class="feature-title">💰 Price Tags</div>
                Use Price Tag Labels (1×1") for display cases and shelves
            </div>
            <div class="feature-item">
                <div class="feature-title">📦 Inventory</div>
                Use Inventory Labels (2×1.5") for storage containers and bulk items
            </div>
            <div class="feature-item">
                <div class="feature-title">🍼 Bottles/Cups</div>
                Use Bottle Labels (2.5×1") for beverages and cylindrical containers
            </div>
        </div>

        <h2>🔧 Troubleshooting</h2>

        <div class="step">
            <div class="step-title">❌ "No products found" or empty search</div>
            <div class="step-content">
                <strong>Solution:</strong>
                <ul>
                    <li>Make sure you have products in your system</li>
                    <li>Go to Products → Product List to check</li>
                    <li>If no products, add them manually or import sample data</li>
                    <li>Try searching by product code instead of name</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <div class="step-title">❌ Labels don't align with paper</div>
            <div class="step-content">
                <strong>Solution:</strong>
                <ul>
                    <li>Check that you selected the correct paper size</li>
                    <li>Verify your label sheets match the selected setting</li>
                    <li>Adjust printer margins in print settings</li>
                    <li>Consider creating a custom barcode setting</li>
                </ul>
            </div>
        </div>

        <div class="step">
            <div class="step-title">❌ Barcode not scanning properly</div>
            <div class="step-content">
                <strong>Solution:</strong>
                <ul>
                    <li>Ensure labels are printed clearly (not blurry)</li>
                    <li>Check printer quality settings</li>
                    <li>Verify barcode symbology matches your scanner</li>
                    <li>Make sure labels are not wrinkled or damaged</li>
                </ul>
            </div>
        </div>

        <h2>📝 Sample Workflow</h2>

        <div class="tip">
            <strong>Example: Printing labels for new coffee products</strong>
            <ol>
                <li>Go to Products → Print Barcode</li>
                <li>Search for "Cappuccino" and add it (quantity: 20)</li>
                <li>Search for "Latte" and add it (quantity: 15)</li>
                <li>Search for "Espresso" and add it (quantity: 10)</li>
                <li>Keep all checkboxes checked (Name, Price, Business Name)</li>
                <li>Select "Cafe Standard Labels" from dropdown</li>
                <li>Click "Submit" to preview</li>
                <li>Click "Print" and print on 2×1 inch label sheets</li>
                <li>Apply labels to coffee cups or storage containers</li>
            </ol>
        </div>

        <div class="quick-actions">
            <h3>🎉 Ready to Print!</h3>
            <p>Your barcode printing system is fully configured. Start by adding a few products and printing test labels to get familiar with the process.</p>
            <a href="../products/print_barcode" class="btn btn-primary">🖨️ Start Printing Barcodes</a>
        </div>
    </div>
</body>
</html>
